version: '3.8'

services:
  # EventStore Database
  eventstore:
    image: eventstore/eventstore:23.10.0-bookworm-slim
    environment:
      - EVENTSTORE_CLUSTER_SIZE=1
      - EVENTSTORE_RUN_PROJECTIONS=All
      - EVENTSTORE_START_STANDARD_PROJECTIONS=true
      - EVENTSTORE_EXT_TCP_PORT=1113
      - EVENTSTORE_HTTP_PORT=2113
      - EVENTSTORE_INSECURE=true
      - EVENTSTORE_ENABLE_EXTERNAL_TCP=true
      - EVENTSTORE_ENABLE_ATOM_PUB_OVER_HTTP=true
    ports:
      - "1113:1113"
      - "2113:2113"
    volumes:
      - eventstore-volume-data:/var/lib/eventstore
      - eventstore-volume-logs:/var/log/eventstore
    networks:
      - enterprise-network

  # PostgreSQL for Management Center
  postgres-management:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: management_center
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres-management-data:/var/lib/postgresql/data
    networks:
      - enterprise-network

  # PostgreSQL for Correspondence
  postgres-correspondence:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: correspondence
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"
    volumes:
      - postgres-correspondence-data:/var/lib/postgresql/data
    networks:
      - enterprise-network

  # MongoDB for Management Center (Write Database)
  mongodb-management-write:
    image: mongo:7.0
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: management_center_write
    ports:
      - "27017:27017"
    volumes:
      - mongodb-management-write-data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - enterprise-network
    command: mongod --replSet rs0

  # MongoDB for Management Center (Read Database)
  mongodb-management-read:
    image: mongo:7.0
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: management_center_read
    ports:
      - "27018:27017"
    volumes:
      - mongodb-management-read-data:/data/db
    networks:
      - enterprise-network
    command: mongod --replSet rs0

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - enterprise-network

  # RabbitMQ for event bus
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: password
      RABBITMQ_DEFAULT_VHOST: /
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
    networks:
      - enterprise-network
    healthcheck:
      test: rabbitmq-diagnostics -q ping
      interval: 30s
      timeout: 30s
      retries: 3

  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: apps/api-gateway/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - JWT_SECRET=your-super-secret-jwt-key
      - MANAGEMENT_SERVICE_HOST=management-center
      - MANAGEMENT_SERVICE_PORT=3001
      - CORRESPONDENCE_SERVICE_HOST=correspondence
      - CORRESPONDENCE_SERVICE_PORT=3002
      - RABBITMQ_URL=amqp://admin:password@rabbitmq:5672
    depends_on:
      - management-center
      - correspondence
      - rabbitmq
    networks:
      - enterprise-network

  # Management Center Service
  management-center:
    build:
      context: .
      dockerfile: apps/management-center/Dockerfile
    ports:
      - "4001:4001"
    environment:
      - NODE_ENV=development
      - PORT=4001
      - MICROSERVICE_PORT=3001
      # PostgreSQL (Legacy)
      - DB_HOST=postgres-management
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - DB_NAME=management_center
      # MongoDB
      - MONGODB_WRITE_URI=************************************************************************************************
      - MONGODB_READ_URI=**********************************************************************************************
      # Event Store and RabbitMQ
      - EVENTSTORE_CONNECTION_STRING=esdb://eventstore:2113?tls=false
      - RABBITMQ_URL=amqp://admin:password@rabbitmq:5672
      - RABBITMQ_EXCHANGE=enterprise.events
      # JWT
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
    depends_on:
      - postgres-management
      - mongodb-management-write
      - mongodb-management-read
      - eventstore
      - rabbitmq
    networks:
      - enterprise-network

  # Correspondence Service
  correspondence:
    build:
      context: .
      dockerfile: apps/correspondence/Dockerfile
    ports:
      - "4002:4002"
    environment:
      - NODE_ENV=development
      - PORT=4002
      - MICROSERVICE_PORT=3002
      - DB_HOST=postgres-correspondence
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - DB_NAME=correspondence
      - EVENTSTORE_CONNECTION_STRING=esdb://eventstore:2113?tls=false
      - RABBITMQ_URL=amqp://admin:password@rabbitmq:5672
    depends_on:
      - postgres-correspondence
      - eventstore
      - rabbitmq
    networks:
      - enterprise-network

volumes:
  eventstore-volume-data:
  eventstore-volume-logs:
  postgres-management-data:
  postgres-correspondence-data:
  mongodb-management-write-data:
  mongodb-management-read-data:
  redis-data:
  rabbitmq-data:

networks:
  enterprise-network:
    driver: bridge
