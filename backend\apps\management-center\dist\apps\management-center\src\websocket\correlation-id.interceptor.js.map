{"version": 3, "file": "correlation-id.interceptor.js", "sourceRoot": "", "sources": ["../../../../../src/websocket/correlation-id.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAMwB;AACxB,+BAA+D;AAC/D,uFAAiF;AACjF,uCAAyC;AACzC,+BAAoC;AAGvB,QAAA,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,MAAM,eAAe,GAAG,CAAC,SAAkB,EAAE,EAAE;IACpD,OAAO,CAAC,MAAW,EAAE,WAAmB,EAAE,UAA8B,EAAE,EAAE;QAC1E,OAAO,CAAC,cAAc,CAAC,4BAAoB,EAAE,SAAS,IAAI,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;IACpF,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,eAAe,mBAI1B;AAGK,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAGnC,YACmB,cAA4C,EAC5C,SAAoB;QADpB,mBAAc,GAAd,cAAc,CAA8B;QAC5C,cAAS,GAAT,SAAS,CAAW;QAJtB,WAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;IAKjE,CAAC;IAEJ,SAAS,CAAC,OAAyB,EAAE,IAAiB;QAEpD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAChD,4BAAoB,EACpB,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAC3C,CAAC;QAEF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC;QACtD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAG1B,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC;QAGD,IAAI,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC;YACpC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAGrD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,aAAa,GAAG,IAAA,SAAM,GAAE,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0CAA0C,aAAa,EAAE,CAAC,CAAC;QAC9E,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,EAAE,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC;QACpD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC3C,MAAM,SAAS,GAAG,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;QAExE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,aAAa,EAAE,CAAC,CAAC;QAG9E,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;QAGtC,MAAM,oBAAoB,GAAG,KAAK,IAAI,EAAE;YACtC,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CACpE,aAAa,EACb,IAAI,CAAC,MAAM,EACX,MAAM,EACN,QAAQ,EACR,MAAM,CACP,CAAC;gBAGF,QAAQ,CAAC,SAAS,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;gBAEtD,OAAO,eAAe,CAAC;YACzB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACxE,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC,CAAC;QAGF,OAAO,IAAI,iBAAU,CAAC,UAAU,CAAC,EAAE;YACjC,oBAAoB,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;gBAE5C,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;gBAG5D,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAChB,IAAA,UAAG,EAAC,KAAK,EAAE,MAAM,EAAE,EAAE;oBAEnB,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,EAAE;wBACvD,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,MAAM;wBACZ,QAAQ;wBACR,MAAM;qBACP,CAAC,CAAC;gBACL,CAAC,CAAC,EACF,IAAA,iBAAU,EAAC,KAAK,EAAE,KAAK,EAAE,EAAE;oBAEzB,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CACnC,aAAa,EACb,KAAK,CAAC,OAAO,IAAI,2BAA2B,CAC7C,CAAC;oBACF,OAAO,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;gBACjC,CAAC,CAAC,CACH,CAAC,SAAS,CAAC;oBACV,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE;wBAGf,UAAU,CAAC,IAAI,CAAC;4BACd,GAAG,eAAe;4BAClB,OAAO,EAAE,2DAA2D;4BACpE,sBAAsB,EAAE,IAAI;yBAC7B,CAAC,CAAC;wBACH,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACxB,CAAC;oBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;wBACf,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAC1B,CAAC;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACf,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,OAAY;QAEhC,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,IAAI,EAAE,CAAC;QAG/B,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7F,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAGD,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACnE,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAGD,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACnD,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAC;QAChB,CAAC;QAGD,OAAO,mBAAmB,CAAC;IAC7B,CAAC;CACF,CAAA;AA1IY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;qCAKwB,8DAA4B;QACjC,gBAAS;GAL5B,wBAAwB,CA0IpC"}