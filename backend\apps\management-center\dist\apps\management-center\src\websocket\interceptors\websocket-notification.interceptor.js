"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WebSocketNotificationInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketNotificationInterceptor = exports.WebSocketNotification = exports.WEBSOCKET_NOTIFICATION_KEY = void 0;
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const websocket_notification_service_1 = require("../websocket-notification.service");
const core_1 = require("@nestjs/core");
exports.WEBSOCKET_NOTIFICATION_KEY = 'websocket_notification';
const WebSocketNotification = (operation) => {
    return (target, propertyKey, descriptor) => {
        Reflect.defineMetadata(exports.WEBSOCKET_NOTIFICATION_KEY, operation || true, descriptor.value);
    };
};
exports.WebSocketNotification = WebSocketNotification;
let WebSocketNotificationInterceptor = WebSocketNotificationInterceptor_1 = class WebSocketNotificationInterceptor {
    constructor(notificationService, reflector) {
        this.notificationService = notificationService;
        this.reflector = reflector;
        this.logger = new common_1.Logger(WebSocketNotificationInterceptor_1.name);
    }
    intercept(context, next) {
        const isEnabled = this.reflector.getAllAndOverride(exports.WEBSOCKET_NOTIFICATION_KEY, [context.getHandler(), context.getClass()]);
        if (!isEnabled) {
            return next.handle();
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user || !user.userId) {
            return next.handle();
        }
        const endpoint = request.route?.path || request.url;
        const method = request.method;
        const operation = typeof isEnabled === 'string' ? isEnabled : undefined;
        const requestId = this.notificationService.createRequestContext(user.userId, endpoint, method);
        request.requestId = requestId;
        this.notificationService.sendAuthenticationPassed(requestId, {
            id: user.userId,
            email: user.email,
            role: user.role,
        });
        this.notificationService.sendProcessingStarted(requestId, operation);
        return next.handle().pipe((0, rxjs_1.tap)((response) => {
            this.notificationService.sendCompleted(requestId, {
                success: true,
                dataCount: Array.isArray(response?.data) ? response.data.length : undefined,
            });
        }), (0, rxjs_1.catchError)((error) => {
            this.notificationService.sendError(requestId, error);
            return (0, rxjs_1.throwError)(() => error);
        }));
    }
};
exports.WebSocketNotificationInterceptor = WebSocketNotificationInterceptor;
exports.WebSocketNotificationInterceptor = WebSocketNotificationInterceptor = WebSocketNotificationInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [websocket_notification_service_1.WebSocketNotificationService,
        core_1.Reflector])
], WebSocketNotificationInterceptor);
//# sourceMappingURL=websocket-notification.interceptor.js.map