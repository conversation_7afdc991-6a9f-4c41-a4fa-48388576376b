"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserEmailVerifiedEvent = void 0;
class UserEmailVerifiedEvent extends common_1.DomainEvent {
    constructor(userId, email, verifiedAt) {
        super('User', userId, 'UserEmailVerified', 1);
        this.userId = userId;
        this.email = email;
        this.verifiedAt = verifiedAt;
    }
}
exports.UserEmailVerifiedEvent = UserEmailVerifiedEvent;
//# sourceMappingURL=user-email-verified.event.js.map