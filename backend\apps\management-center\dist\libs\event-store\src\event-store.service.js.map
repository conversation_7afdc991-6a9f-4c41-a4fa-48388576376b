{"version": 3, "file": "event-store.service.js", "sourceRoot": "", "sources": ["../../../../../../libs/event-store/src/event-store.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,qDAAuF;AAEvF,+BAAoC;AAG7B,IAAM,iBAAiB,yBAAvB,MAAM,iBAAiB;IAI5B;QAHiB,WAAM,GAAG,IAAI,eAAM,CAAC,mBAAiB,CAAC,IAAI,CAAC,CAAC;QAI3D,IAAI,CAAC,MAAM,GAAG,8BAAkB,CAAC,gBAAgB,CAC/C,OAAO,CAAC,GAAG,CAAC,4BAA4B,IAAI,iCAAiC,CAC9E,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,UAAU,CACd,WAAmB,EACnB,MAAqB,EACrB,eAAuB;QAEvB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YACnD,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CACnC,IAAA,qBAAS,EAAC;gBACR,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,KAAK,CAAC,SAAS;gBACrB,IAAI,EAAE;oBACJ,GAAG,KAAK,CAAC,SAAS;oBAClB,WAAW,EAAE,KAAK,CAAC,WAAW;oBAC9B,aAAa,EAAE,KAAK,CAAC,aAAa;oBAClC,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,UAAU,EAAE,KAAK,CAAC,UAAU;oBAC5B,QAAQ,EAAE,KAAK,CAAC,QAAQ;iBACzB;aACF,CAAC,CACH,CAAC;YAEF,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,SAAS,EAAE;gBACtD,gBAAgB,EAAE,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC;aACjF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,MAAM,CAAC,MAAM,yBAAyB,WAAW,EAAE,CAAC,CAAC;QAChF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;YAC/E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,WAAmB,EAAE,WAAoB;QACvD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YACnD,MAAM,MAAM,GAAkB,EAAE,CAAC;YAEjC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,EAAE;gBACpD,SAAS,EAAE,oBAAQ;gBACnB,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,iBAAK;aACxD,CAAC,CAAC;YAEH,IAAI,KAAK,EAAE,MAAM,aAAa,IAAI,UAAU,EAAE,CAAC;gBAC7C,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;oBACxB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;oBACzD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,YAAqB;QACtC,IAAI,CAAC;YACH,MAAM,MAAM,GAAkB,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;gBAClC,SAAS,EAAE,oBAAQ;gBACnB,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,iBAAK;aAC1D,CAAC,CAAC;YAEH,IAAI,KAAK,EAAE,MAAM,aAAa,IAAI,OAAO,EAAE,CAAC;gBAC1C,IAAI,aAAa,CAAC,KAAK,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBACzE,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;oBACzD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,WAAmB;QACvC,OAAO,aAAa,WAAW,EAAE,CAAC;IACpC,CAAC;IAEO,gBAAgB,CAAC,SAAc;QACrC,OAAO;YACL,OAAO,EAAE,SAAS,CAAC,EAAE;YACrB,SAAS,EAAE,SAAS,CAAC,IAAI;YACzB,WAAW,EAAE,SAAS,CAAC,IAAI,CAAC,WAAW;YACvC,aAAa,EAAE,SAAS,CAAC,IAAI,CAAC,aAAa;YAC3C,YAAY,EAAE,SAAS,CAAC,IAAI,CAAC,YAAY;YACzC,SAAS,EAAE,SAAS,CAAC,IAAI;YACzB,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,QAAQ;YACjC,UAAU,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;SAChD,CAAC;IACJ,CAAC;CACF,CAAA;AAzGY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;;GACA,iBAAiB,CAyG7B"}