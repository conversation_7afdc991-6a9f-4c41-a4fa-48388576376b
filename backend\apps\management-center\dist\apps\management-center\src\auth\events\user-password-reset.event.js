"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPasswordResetEvent = void 0;
class UserPasswordResetEvent extends common_1.DomainEvent {
    constructor(userId, email, resetAt, ipAddress) {
        super('User', userId, 'UserPasswordReset', 1);
        this.userId = userId;
        this.email = email;
        this.resetAt = resetAt;
        this.ipAddress = ipAddress;
    }
}
exports.UserPasswordResetEvent = UserPasswordResetEvent;
//# sourceMappingURL=user-password-reset.event.js.map