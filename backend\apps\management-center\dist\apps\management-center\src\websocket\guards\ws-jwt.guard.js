"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WsJwtGuard_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WsJwtGuard = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
let WsJwtGuard = WsJwtGuard_1 = class WsJwtGuard {
    constructor(jwtService) {
        this.jwtService = jwtService;
        this.logger = new common_1.Logger(WsJwtGuard_1.name);
    }
    async canActivate(context) {
        try {
            const client = context.switchToWs().getClient();
            if (client.data?.userId) {
                return true;
            }
            const token = this.extractTokenFromClient(client);
            if (!token) {
                this.logger.warn(`WebSocket guard: No token provided for client ${client.id}`);
                return false;
            }
            const payload = await this.jwtService.verifyAsync(token);
            const userId = payload.sub || payload.userId;
            if (!userId) {
                this.logger.warn(`WebSocket guard: Invalid token payload for client ${client.id}`);
                return false;
            }
            client.data.userId = userId;
            client.data.user = payload;
            return true;
        }
        catch (error) {
            this.logger.error(`WebSocket guard authentication failed:`, error.message);
            return false;
        }
    }
    extractTokenFromClient(client) {
        const authToken = client.handshake.auth?.token;
        if (authToken) {
            return authToken;
        }
        const authHeader = client.handshake.headers?.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            return authHeader.replace('Bearer ', '');
        }
        const queryToken = client.handshake.query?.token;
        if (queryToken && typeof queryToken === 'string') {
            return queryToken;
        }
        return null;
    }
};
exports.WsJwtGuard = WsJwtGuard;
exports.WsJwtGuard = WsJwtGuard = WsJwtGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService])
], WsJwtGuard);
//# sourceMappingURL=ws-jwt.guard.js.map