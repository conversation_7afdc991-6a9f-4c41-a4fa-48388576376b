import { Injectable, Logger } from '@nestjs/common';
import { WebSocketGateway } from './websocket.gateway';
import { RequestStatusMessage } from './websocket.gateway';
import { v4 as uuidv4 } from 'uuid';

export interface RequestContext {
  requestId: string;
  userId: string;
  endpoint: string;
  method: string;
  startTime: Date;
}

@Injectable()
export class WebSocketNotificationService {
  private readonly logger = new Logger(WebSocketNotificationService.name);
  private activeRequests = new Map<string, RequestContext>();

  constructor(private readonly webSocketGateway: WebSocketGateway) {}

  // Create a new request context and send "received" status
  createRequestContext(userId: string, endpoint: string, method: string): string {
    const requestId = uuidv4();
    const context: RequestContext = {
      requestId,
      userId,
      endpoint,
      method,
      startTime: new Date(),
    };

    this.activeRequests.set(requestId, context);

    // Send "received" status
    this.sendStatus(requestId, 'received', `Request received: ${method} ${endpoint}`);

    this.logger.log(`Created request context ${requestId} for user ${userId}`);
    return requestId;
  }

  // Send authentication passed status
  sendAuthenticationPassed(requestId: string, userInfo?: any) {
    const message = userInfo 
      ? `Authentication passed for user: ${userInfo.email || userInfo.id}`
      : 'Authentication passed';
    
    this.sendStatus(requestId, 'authenticated', message, userInfo);
  }

  // Send processing status
  sendProcessingStarted(requestId: string, operation?: string) {
    const message = operation 
      ? `Processing started: ${operation}`
      : 'Server is processing the request';
    
    this.sendStatus(requestId, 'processing', message);
  }

  // Send completion status
  sendCompleted(requestId: string, result?: any) {
    this.sendStatus(requestId, 'completed', 'Request completed successfully', result);
    this.cleanupRequest(requestId);
  }

  // Send error status
  sendError(requestId: string, error: string | Error) {
    const errorMessage = error instanceof Error ? error.message : error;
    this.sendStatus(requestId, 'error', 'Request failed', undefined, errorMessage);
    this.cleanupRequest(requestId);
  }

  // Generic method to send status updates
  private sendStatus(
    requestId: string,
    status: RequestStatusMessage['status'],
    message: string,
    data?: any,
    error?: string,
  ) {
    const context = this.activeRequests.get(requestId);
    if (!context) {
      this.logger.warn(`Request context not found for ID: ${requestId}`);
      return;
    }

    const statusMessage: RequestStatusMessage = {
      requestId,
      status,
      message,
      timestamp: new Date(),
      data,
      error,
    };

    // Send to specific user
    this.webSocketGateway.sendStatusToUser(context.userId, statusMessage);
    
    // Also send to request-specific subscribers
    this.webSocketGateway.sendStatusToRequest(requestId, statusMessage);

    this.logger.debug(`Sent ${status} status for request ${requestId}`);
  }

  // Clean up completed/failed requests
  private cleanupRequest(requestId: string) {
    this.activeRequests.delete(requestId);
    this.logger.debug(`Cleaned up request context ${requestId}`);
  }

  // Get active requests count
  getActiveRequestsCount(): number {
    return this.activeRequests.size;
  }

  // Get active requests for a user
  getUserActiveRequests(userId: string): RequestContext[] {
    return Array.from(this.activeRequests.values()).filter(
      context => context.userId === userId
    );
  }

  // Get all active requests (admin only)
  getAllActiveRequests(): RequestContext[] {
    return Array.from(this.activeRequests.values());
  }

  // Send custom notification to user
  sendCustomNotification(userId: string, message: string, data?: any) {
    const customMessage: RequestStatusMessage = {
      requestId: 'notification',
      status: 'completed',
      message,
      timestamp: new Date(),
      data,
    };

    this.webSocketGateway.sendStatusToUser(userId, customMessage);
    this.logger.debug(`Sent custom notification to user ${userId}: ${message}`);
  }

  // Broadcast notification to all users
  broadcastNotification(message: string, data?: any) {
    const broadcastMessage: RequestStatusMessage = {
      requestId: 'broadcast',
      status: 'completed',
      message,
      timestamp: new Date(),
      data,
    };

    this.webSocketGateway.broadcastStatus(broadcastMessage);
    this.logger.debug(`Broadcasted notification: ${message}`);
  }
}
