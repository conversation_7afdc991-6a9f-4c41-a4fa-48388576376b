"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiResponseDecorator = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const response_dto_1 = require("../dtos/response.dto");
const ApiResponseDecorator = (model, description) => {
    return (0, common_1.applyDecorators)((0, swagger_1.ApiExtraModels)(response_dto_1.ApiResponseDto, model), (0, swagger_1.ApiOkResponse)({
        description: description || 'Successful operation',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_dto_1.ApiResponseDto) },
                {
                    properties: {
                        data: {
                            $ref: (0, swagger_1.getSchemaPath)(model),
                        },
                    },
                },
            ],
        },
    }));
};
exports.ApiResponseDecorator = ApiResponseDecorator;
//# sourceMappingURL=api-response.decorator.js.map