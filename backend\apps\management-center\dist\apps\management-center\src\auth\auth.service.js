"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const cqrs_1 = require("@nestjs/cqrs");
const bcrypt = require("bcrypt");
const uuid_1 = require("uuid");
const user_schema_1 = require("./schemas/user.schema");
const refresh_token_schema_1 = require("./schemas/refresh-token.schema");
const register_user_command_1 = require("./commands/register-user.command");
const login_user_command_1 = require("./commands/login-user.command");
const user_email_verified_event_1 = require("./events/user-email-verified.event");
const user_password_reset_event_1 = require("./events/user-password-reset.event");
let AuthService = class AuthService {
    constructor(userWriteModel, userReadModel, refreshTokenWriteModel, refreshTokenReadModel, jwtService, commandBus, eventBus) {
        this.userWriteModel = userWriteModel;
        this.userReadModel = userReadModel;
        this.refreshTokenWriteModel = refreshTokenWriteModel;
        this.refreshTokenReadModel = refreshTokenReadModel;
        this.jwtService = jwtService;
        this.commandBus = commandBus;
        this.eventBus = eventBus;
    }
    async register(registerDto) {
        if (registerDto.password !== registerDto.confirmPassword) {
            throw new common_1.BadRequestException('Passwords do not match');
        }
        const existingUser = await this.userReadModel.findOne({ email: registerDto.email }).exec();
        if (existingUser) {
            throw new common_1.ConflictException('User with this email already exists');
        }
        const command = new register_user_command_1.RegisterUserCommand(registerDto);
        const user = await this.commandBus.execute(command);
        const tokens = await this.generateTokens(user);
        return {
            ...tokens,
            user: this.mapUserToResponse(user),
        };
    }
    async login(loginDto, userAgent, ipAddress) {
        const command = new login_user_command_1.LoginUserCommand(loginDto, userAgent, ipAddress);
        const { user, refreshToken } = await this.commandBus.execute(command);
        const accessToken = await this.generateAccessToken(user);
        return {
            accessToken,
            refreshToken: refreshToken.token,
            expiresIn: 24 * 60 * 60,
            tokenType: 'Bearer',
            user: this.mapUserToResponse(user),
        };
    }
    async validateUser(email, password) {
        const user = await this.userReadModel.findOne({ email }).exec();
        if (!user) {
            return null;
        }
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            return null;
        }
        if (!user.isActive()) {
            throw new common_1.UnauthorizedException('Account is not active or email not verified');
        }
        return user;
    }
    async refreshTokens(refreshToken) {
        const tokenDoc = await this.refreshTokenReadModel
            .findOne({ token: refreshToken })
            .populate('userId')
            .exec();
        if (!tokenDoc || !tokenDoc.isActive()) {
            throw new common_1.UnauthorizedException('Invalid or expired refresh token');
        }
        const user = await this.userReadModel.findById(tokenDoc.userId).exec();
        if (!user || !user.isActive()) {
            throw new common_1.UnauthorizedException('User not found or inactive');
        }
        tokenDoc.revoke();
        await tokenDoc.save();
        const tokens = await this.generateTokens(user);
        return tokens;
    }
    async logout(refreshToken) {
        const tokenDoc = await this.refreshTokenWriteModel.findOne({ token: refreshToken }).exec();
        if (tokenDoc) {
            tokenDoc.revoke();
            await tokenDoc.save();
        }
    }
    async revokeAllTokens(userId) {
        await this.refreshTokenWriteModel.updateMany({ userId: new mongoose_2.Types.ObjectId(userId), revoked: false }, { revoked: true, revokedAt: new Date() }).exec();
    }
    async verifyEmail(verifyEmailDto) {
        const user = await this.userWriteModel.findOne({
            emailVerificationToken: verifyEmailDto.token,
            emailVerificationExpires: { $gt: new Date() }
        }).exec();
        if (!user) {
            throw new common_1.BadRequestException('Invalid or expired verification token');
        }
        user.emailVerified = true;
        user.status = user_schema_1.UserStatus.ACTIVE;
        user.emailVerificationToken = undefined;
        user.emailVerificationExpires = undefined;
        user.updatedAt = new Date();
        await user.save();
        const event = new user_email_verified_event_1.UserEmailVerifiedEvent(user._id.toString(), user.email, new Date());
        this.eventBus.publish(event);
        return this.mapUserToResponse(user);
    }
    async resendVerificationEmail(resendDto) {
        const user = await this.userWriteModel.findOne({ email: resendDto.email }).exec();
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        if (user.emailVerified) {
            throw new common_1.BadRequestException('Email is already verified');
        }
        const emailVerificationToken = (0, uuid_1.v4)();
        const emailVerificationExpires = new Date();
        emailVerificationExpires.setHours(emailVerificationExpires.getHours() + 24);
        user.emailVerificationToken = emailVerificationToken;
        user.emailVerificationExpires = emailVerificationExpires;
        user.updatedAt = new Date();
        await user.save();
    }
    async forgotPassword(forgotPasswordDto) {
        const user = await this.userWriteModel.findOne({ email: forgotPasswordDto.email }).exec();
        if (!user) {
            return;
        }
        const passwordResetToken = (0, uuid_1.v4)();
        const passwordResetExpires = new Date();
        passwordResetExpires.setHours(passwordResetExpires.getHours() + 1);
        user.passwordResetToken = passwordResetToken;
        user.passwordResetExpires = passwordResetExpires;
        user.updatedAt = new Date();
        await user.save();
    }
    async resetPassword(resetPasswordDto) {
        if (resetPasswordDto.newPassword !== resetPasswordDto.confirmPassword) {
            throw new common_1.BadRequestException('Passwords do not match');
        }
        const user = await this.userWriteModel.findOne({
            passwordResetToken: resetPasswordDto.token,
            passwordResetExpires: { $gt: new Date() }
        }).exec();
        if (!user) {
            throw new common_1.BadRequestException('Invalid or expired reset token');
        }
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(resetPasswordDto.newPassword, saltRounds);
        user.password = hashedPassword;
        user.passwordResetToken = undefined;
        user.passwordResetExpires = undefined;
        user.updatedAt = new Date();
        await user.save();
        const event = new user_password_reset_event_1.UserPasswordResetEvent(user._id.toString(), user.email, new Date());
        this.eventBus.publish(event);
        await this.revokeAllTokens(user._id.toString());
    }
    async generateTokens(user) {
        const accessToken = await this.generateAccessToken(user);
        const refreshToken = await this.generateRefreshToken(user);
        return {
            accessToken,
            refreshToken: refreshToken.token,
            expiresIn: 24 * 60 * 60,
            tokenType: 'Bearer',
        };
    }
    async generateAccessToken(user) {
        const payload = {
            sub: user._id.toString(),
            email: user.email,
            role: user.role,
            organizationId: user.organizationId?.toString(),
        };
        return this.jwtService.sign(payload);
    }
    async generateRefreshToken(user) {
        const token = (0, uuid_1.v4)();
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 7);
        const refreshToken = new this.refreshTokenWriteModel({
            token,
            userId: user._id,
            expiresAt,
        });
        return refreshToken.save();
    }
    mapUserToResponse(user) {
        return {
            id: user._id.toString(),
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            fullName: user.fullName,
            phone: user.phone,
            role: user.role,
            status: user.status,
            emailVerified: user.emailVerified,
            organizationId: user.organizationId?.toString(),
            lastLoginAt: user.lastLoginAt,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
        };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name, 'write')),
    __param(1, (0, mongoose_1.InjectModel)(user_schema_1.User.name, 'read')),
    __param(2, (0, mongoose_1.InjectModel)(refresh_token_schema_1.RefreshToken.name, 'write')),
    __param(3, (0, mongoose_1.InjectModel)(refresh_token_schema_1.RefreshToken.name, 'read')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        jwt_1.JwtService,
        cqrs_1.CommandBus,
        cqrs_1.EventBus])
], AuthService);
//# sourceMappingURL=auth.service.js.map