import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Socket } from 'socket.io';

@Injectable()
export class WsJwtGuard implements CanActivate {
  private readonly logger = new Logger(WsJwtGuard.name);

  constructor(private readonly jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const client: Socket = context.switchToWs().getClient();
      
      // Check if user is already authenticated from connection
      if (client.data?.userId) {
        return true;
      }

      // Extract token from handshake or message
      const token = this.extractTokenFromClient(client);
      
      if (!token) {
        this.logger.warn(`WebSocket guard: No token provided for client ${client.id}`);
        return false;
      }

      // Verify JWT token
      const payload = await this.jwtService.verifyAsync(token);
      const userId = payload.sub || payload.userId;

      if (!userId) {
        this.logger.warn(`WebSocket guard: Invalid token payload for client ${client.id}`);
        return false;
      }

      // Store user data in client
      client.data.userId = userId;
      client.data.user = payload;

      return true;
    } catch (error) {
      this.logger.error(`WebSocket guard authentication failed:`, error.message);
      return false;
    }
  }

  private extractTokenFromClient(client: Socket): string | null {
    // Try to get token from handshake auth
    const authToken = client.handshake.auth?.token;
    if (authToken) {
      return authToken;
    }

    // Try to get token from authorization header
    const authHeader = client.handshake.headers?.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.replace('Bearer ', '');
    }

    // Try to get token from query parameters
    const queryToken = client.handshake.query?.token;
    if (queryToken && typeof queryToken === 'string') {
      return queryToken;
    }

    return null;
  }
}
