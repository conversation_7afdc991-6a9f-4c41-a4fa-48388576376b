"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketModule = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const websocket_gateway_1 = require("./websocket.gateway");
const websocket_notification_service_1 = require("./websocket-notification.service");
const ws_jwt_guard_1 = require("./guards/ws-jwt.guard");
const websocket_notification_interceptor_1 = require("./interceptors/websocket-notification.interceptor");
const websocket_test_controller_1 = require("./websocket-test.controller");
const request_status_service_1 = require("./request-status.service");
const request_status_controller_1 = require("./request-status.controller");
const unified_request_tracker_service_1 = require("./unified-request-tracker.service");
const unified_status_controller_1 = require("./unified-status.controller");
const correlation_id_interceptor_1 = require("./correlation-id.interceptor");
let WebSocketModule = class WebSocketModule {
};
exports.WebSocketModule = WebSocketModule;
exports.WebSocketModule = WebSocketModule = __decorate([
    (0, common_1.Module)({
        imports: [
            jwt_1.JwtModule.register({
                secret: process.env.JWT_SECRET || 'your-secret-key',
                signOptions: { expiresIn: '24h' },
            }),
        ],
        controllers: [
            websocket_test_controller_1.WebSocketTestController,
            request_status_controller_1.RequestStatusController,
            unified_status_controller_1.UnifiedStatusController,
            unified_status_controller_1.ManagementCenterStatusController
        ],
        providers: [
            websocket_gateway_1.WebSocketGateway,
            websocket_notification_service_1.WebSocketNotificationService,
            request_status_service_1.RequestStatusService,
            unified_request_tracker_service_1.UnifiedRequestTrackerService,
            ws_jwt_guard_1.WsJwtGuard,
            websocket_notification_interceptor_1.WebSocketNotificationInterceptor,
            correlation_id_interceptor_1.CorrelationIdInterceptor,
        ],
        exports: [
            websocket_gateway_1.WebSocketGateway,
            websocket_notification_service_1.WebSocketNotificationService,
            request_status_service_1.RequestStatusService,
            unified_request_tracker_service_1.UnifiedRequestTrackerService,
            websocket_notification_interceptor_1.WebSocketNotificationInterceptor,
            correlation_id_interceptor_1.CorrelationIdInterceptor,
        ],
    })
], WebSocketModule);
//# sourceMappingURL=websocket.module.js.map