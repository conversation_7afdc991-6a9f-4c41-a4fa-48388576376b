"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RequestStatusService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestStatusService = void 0;
const common_1 = require("@nestjs/common");
let RequestStatusService = RequestStatusService_1 = class RequestStatusService {
    constructor() {
        this.logger = new common_1.Logger(RequestStatusService_1.name);
        this.requestStatuses = new Map();
        this.TTL_COMPLETED = 5 * 60 * 1000;
        this.TTL_ACTIVE = 30 * 60 * 1000;
        setInterval(() => this.cleanupOldRequests(), 60 * 1000);
    }
    createRequestStatus(requestId, userId, endpoint, method) {
        const status = {
            requestId,
            userId,
            endpoint,
            method,
            status: 'received',
            message: `Request received: ${method} ${endpoint}`,
            startTime: new Date(),
            lastUpdated: new Date(),
            progress: 0,
        };
        this.requestStatuses.set(requestId, status);
        this.logger.debug(`Created request status for ${requestId}`);
        return status;
    }
    updateRequestStatus(requestId, status, message, data, error, progress) {
        const requestStatus = this.requestStatuses.get(requestId);
        if (!requestStatus) {
            this.logger.warn(`Request status not found for ID: ${requestId}`);
            return null;
        }
        requestStatus.status = status;
        requestStatus.message = message;
        requestStatus.lastUpdated = new Date();
        if (data !== undefined) {
            requestStatus.data = data;
        }
        if (error !== undefined) {
            requestStatus.error = error;
        }
        if (progress !== undefined) {
            requestStatus.progress = progress;
        }
        this.requestStatuses.set(requestId, requestStatus);
        this.logger.debug(`Updated request status ${requestId}: ${status}`);
        return requestStatus;
    }
    getRequestStatus(requestId) {
        return this.requestStatuses.get(requestId) || null;
    }
    getUserRequestStatuses(userId) {
        return Array.from(this.requestStatuses.values()).filter(status => status.userId === userId);
    }
    getUserActiveRequests(userId) {
        return this.getUserRequestStatuses(userId).filter(status => !['completed', 'error'].includes(status.status));
    }
    completeRequest(requestId, data) {
        return this.updateRequestStatus(requestId, 'completed', 'Request completed successfully', data, undefined, 100);
    }
    failRequest(requestId, error) {
        return this.updateRequestStatus(requestId, 'error', 'Request failed', undefined, error, undefined);
    }
    setAuthenticated(requestId, userInfo) {
        return this.updateRequestStatus(requestId, 'authenticated', 'Authentication passed', userInfo, undefined, 25);
    }
    setProcessing(requestId, operation, progress) {
        const message = operation
            ? `Processing: ${operation}`
            : 'Server is processing the request';
        return this.updateRequestStatus(requestId, 'processing', message, undefined, undefined, progress || 50);
    }
    deleteRequestStatus(requestId) {
        const deleted = this.requestStatuses.delete(requestId);
        if (deleted) {
            this.logger.debug(`Deleted request status ${requestId}`);
        }
        return deleted;
    }
    cleanupOldRequests() {
        const now = new Date().getTime();
        let cleanedCount = 0;
        for (const [requestId, status] of this.requestStatuses.entries()) {
            const age = now - status.lastUpdated.getTime();
            const isCompleted = ['completed', 'error'].includes(status.status);
            const shouldCleanup = isCompleted
                ? age > this.TTL_COMPLETED
                : age > this.TTL_ACTIVE;
            if (shouldCleanup) {
                this.requestStatuses.delete(requestId);
                cleanedCount++;
            }
        }
        if (cleanedCount > 0) {
            this.logger.debug(`Cleaned up ${cleanedCount} old request statuses`);
        }
    }
    getStatistics() {
        const statuses = Array.from(this.requestStatuses.values());
        return {
            totalRequests: statuses.length,
            activeRequests: statuses.filter(s => !['completed', 'error'].includes(s.status)).length,
            completedRequests: statuses.filter(s => s.status === 'completed').length,
            errorRequests: statuses.filter(s => s.status === 'error').length,
            statusBreakdown: {
                received: statuses.filter(s => s.status === 'received').length,
                authenticated: statuses.filter(s => s.status === 'authenticated').length,
                processing: statuses.filter(s => s.status === 'processing').length,
                completed: statuses.filter(s => s.status === 'completed').length,
                error: statuses.filter(s => s.status === 'error').length,
            }
        };
    }
};
exports.RequestStatusService = RequestStatusService;
exports.RequestStatusService = RequestStatusService = RequestStatusService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], RequestStatusService);
//# sourceMappingURL=request-status.service.js.map