import {
  Injectable,
  NestInterceptor,
  Exec<PERSON><PERSON><PERSON>x<PERSON>,
  CallH<PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable, tap, catchError, throwError } from 'rxjs';
import { WebSocketNotificationService } from '../websocket-notification.service';
import { Reflector } from '@nestjs/core';

// Decorator to enable WebSocket notifications for specific endpoints
export const WEBSOCKET_NOTIFICATION_KEY = 'websocket_notification';
export const WebSocketNotification = (operation?: string) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    Reflect.defineMetadata(WEBSOCKET_NOTIFICATION_KEY, operation || true, descriptor.value);
  };
};

@Injectable()
export class WebSocketNotificationInterceptor implements NestInterceptor {
  private readonly logger = new Logger(WebSocketNotificationInterceptor.name);

  constructor(
    private readonly notificationService: WebSocketNotificationService,
    private readonly reflector: Reflector,
  ) {}

  intercept(context: ExecutionContext, next: <PERSON><PERSON><PERSON><PERSON>): Observable<any> {
    // Check if WebSocket notifications are enabled for this endpoint
    const isEnabled = this.reflector.getAllAndOverride<boolean | string>(
      WEBSOCKET_NOTIFICATION_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!isEnabled) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // Skip if no authenticated user
    if (!user || !user.userId) {
      return next.handle();
    }

    const endpoint = request.route?.path || request.url;
    const method = request.method;
    const operation = typeof isEnabled === 'string' ? isEnabled : undefined;

    // Create request context and send "received" status
    const requestId = this.notificationService.createRequestContext(
      user.userId,
      endpoint,
      method,
    );

    // Add requestId to request for later use
    request.requestId = requestId;

    // Send authentication passed status
    this.notificationService.sendAuthenticationPassed(requestId, {
      id: user.userId,
      email: user.email,
      role: user.role,
    });

    // Send processing started status
    this.notificationService.sendProcessingStarted(requestId, operation);

    return next.handle().pipe(
      tap((response) => {
        // Send completion status
        this.notificationService.sendCompleted(requestId, {
          success: true,
          dataCount: Array.isArray(response?.data) ? response.data.length : undefined,
        });
      }),
      catchError((error) => {
        // Send error status
        this.notificationService.sendError(requestId, error);
        return throwError(() => error);
      }),
    );
  }
}
