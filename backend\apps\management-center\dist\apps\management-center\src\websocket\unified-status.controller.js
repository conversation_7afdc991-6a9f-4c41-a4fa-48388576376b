"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ManagementCenterStatusController = exports.UnifiedStatusController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const unified_request_tracker_service_1 = require("./unified-request-tracker.service");
const common_2 = require("../../../../libs/common/src");
let UnifiedStatusController = class UnifiedStatusController {
    constructor(requestTracker) {
        this.requestTracker = requestTracker;
    }
    async checkStatus(domain, correlationId, req) {
        const user = req.user;
        const status = await this.requestTracker.getRequestStatus(correlationId, domain, user.userId);
        if (!status) {
            return common_2.ApiResponseDto.error(`Request not found for correlation ID: ${correlationId} in domain: ${domain}`, 404);
        }
        return common_2.ApiResponseDto.success(status, 'Request status retrieved');
    }
    async getDomainRequests(domain, activeOnly, req) {
        const user = req.user;
        const requests = await this.requestTracker.getDomainRequests(domain, user.userId);
        const filteredRequests = activeOnly === 'true'
            ? requests.filter(r => !['completed', 'error'].includes(r.status))
            : requests;
        return common_2.ApiResponseDto.success({
            domain,
            requests: filteredRequests,
            total: filteredRequests.length,
            active: filteredRequests.filter(r => !['completed', 'error'].includes(r.status)).length,
        }, `Requests retrieved for domain: ${domain}`);
    }
};
exports.UnifiedStatusController = UnifiedStatusController;
__decorate([
    (0, common_1.Get)(':correlationId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Check request status by domain and correlation ID',
        description: 'Client-generated correlation ID used to check request status across different domains'
    }),
    (0, swagger_1.ApiParam)({ name: 'domain', description: 'Service domain (management-center, correspondence, etc.)' }),
    (0, swagger_1.ApiParam)({ name: 'correlationId', description: 'Client-generated correlation ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Request status retrieved' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Request not found' }),
    __param(0, (0, common_1.Param)('domain')),
    __param(1, (0, common_1.Param)('correlationId')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], UnifiedStatusController.prototype, "checkStatus", null);
__decorate([
    (0, common_1.Get)(''),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all requests for a domain and user',
        description: 'List all requests for the current user in the specified domain'
    }),
    (0, swagger_1.ApiParam)({ name: 'domain', description: 'Service domain' }),
    (0, swagger_1.ApiQuery)({ name: 'active', required: false, description: 'Filter for active requests only' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Domain requests retrieved' }),
    __param(0, (0, common_1.Param)('domain')),
    __param(1, (0, common_1.Query)('active')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], UnifiedStatusController.prototype, "getDomainRequests", null);
exports.UnifiedStatusController = UnifiedStatusController = __decorate([
    (0, swagger_1.ApiTags)('Unified Status Check'),
    (0, common_1.Controller)('api/:domain/check-status'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [unified_request_tracker_service_1.UnifiedRequestTrackerService])
], UnifiedStatusController);
let ManagementCenterStatusController = class ManagementCenterStatusController {
    constructor(requestTracker) {
        this.requestTracker = requestTracker;
    }
    async checkManagementStatus(correlationId, req) {
        const user = req.user;
        const status = await this.requestTracker.getRequestStatus(correlationId, 'management-center', user.userId);
        if (!status) {
            return common_2.ApiResponseDto.error(`Request not found for correlation ID: ${correlationId}`, 404);
        }
        return common_2.ApiResponseDto.success(status, 'Request status retrieved');
    }
    async getManagementRequests(activeOnly, req) {
        const user = req.user;
        const requests = await this.requestTracker.getDomainRequests('management-center', user.userId);
        const filteredRequests = activeOnly === 'true'
            ? requests.filter(r => !['completed', 'error'].includes(r.status))
            : requests;
        return common_2.ApiResponseDto.success({
            domain: 'management-center',
            requests: filteredRequests,
            total: filteredRequests.length,
            active: filteredRequests.filter(r => !['completed', 'error'].includes(r.status)).length,
        }, 'Management center requests retrieved');
    }
};
exports.ManagementCenterStatusController = ManagementCenterStatusController;
__decorate([
    (0, common_1.Get)(':correlationId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Check management-center request status',
        description: 'Shorthand endpoint for checking management-center domain requests'
    }),
    (0, swagger_1.ApiParam)({ name: 'correlationId', description: 'Client-generated correlation ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Request status retrieved' }),
    __param(0, (0, common_1.Param)('correlationId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ManagementCenterStatusController.prototype, "checkManagementStatus", null);
__decorate([
    (0, common_1.Get)(''),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all management-center requests for user',
        description: 'List all management-center requests for the current user'
    }),
    (0, swagger_1.ApiQuery)({ name: 'active', required: false, description: 'Filter for active requests only' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Management center requests retrieved' }),
    __param(0, (0, common_1.Query)('active')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ManagementCenterStatusController.prototype, "getManagementRequests", null);
exports.ManagementCenterStatusController = ManagementCenterStatusController = __decorate([
    (0, swagger_1.ApiTags)('Management Center Status'),
    (0, common_1.Controller)('check-status'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [unified_request_tracker_service_1.UnifiedRequestTrackerService])
], ManagementCenterStatusController);
//# sourceMappingURL=unified-status.controller.js.map