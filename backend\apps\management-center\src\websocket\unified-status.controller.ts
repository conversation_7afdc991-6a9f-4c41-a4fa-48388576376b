import { Controller, Get, Param, UseGuards, Request, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UnifiedRequestTrackerService, RequestResponse } from './unified-request-tracker.service';
import { ApiResponseDto } from '@enterprise/common';

@ApiTags('Unified Status Check')
@Controller('api/:domain/check-status')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UnifiedStatusController {
  constructor(private readonly requestTracker: UnifiedRequestTrackerService) {}

  @Get(':correlationId')
  @ApiOperation({ 
    summary: 'Check request status by domain and correlation ID',
    description: 'Client-generated correlation ID used to check request status across different domains'
  })
  @ApiParam({ name: 'domain', description: 'Service domain (management-center, correspondence, etc.)' })
  @ApiParam({ name: 'correlationId', description: 'Client-generated correlation ID' })
  @ApiResponse({ status: 200, description: 'Request status retrieved' })
  @ApiResponse({ status: 404, description: 'Request not found' })
  async checkStatus(
    @Param('domain') domain: string,
    @Param('correlationId') correlationId: string,
    @Request() req: any,
  ) {
    const user = req.user;
    
    const status = await this.requestTracker.getRequestStatus(
      correlationId, 
      domain, 
      user.userId
    );

    if (!status) {
      return ApiResponseDto.error(
        `Request not found for correlation ID: ${correlationId} in domain: ${domain}`, 
        404
      );
    }

    return ApiResponseDto.success(status, 'Request status retrieved');
  }

  @Get('')
  @ApiOperation({ 
    summary: 'Get all requests for a domain and user',
    description: 'List all requests for the current user in the specified domain'
  })
  @ApiParam({ name: 'domain', description: 'Service domain' })
  @ApiQuery({ name: 'active', required: false, description: 'Filter for active requests only' })
  @ApiResponse({ status: 200, description: 'Domain requests retrieved' })
  async getDomainRequests(
    @Param('domain') domain: string,
    @Query('active') activeOnly: string,
    @Request() req: any,
  ) {
    const user = req.user;
    
    const requests = await this.requestTracker.getDomainRequests(domain, user.userId);
    
    const filteredRequests = activeOnly === 'true' 
      ? requests.filter(r => !['completed', 'error'].includes(r.status))
      : requests;

    return ApiResponseDto.success({
      domain,
      requests: filteredRequests,
      total: filteredRequests.length,
      active: filteredRequests.filter(r => !['completed', 'error'].includes(r.status)).length,
    }, `Requests retrieved for domain: ${domain}`);
  }
}

// Also create a simple status controller for the management-center domain
@ApiTags('Management Center Status')
@Controller('check-status')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ManagementCenterStatusController {
  constructor(private readonly requestTracker: UnifiedRequestTrackerService) {}

  @Get(':correlationId')
  @ApiOperation({ 
    summary: 'Check management-center request status',
    description: 'Shorthand endpoint for checking management-center domain requests'
  })
  @ApiParam({ name: 'correlationId', description: 'Client-generated correlation ID' })
  @ApiResponse({ status: 200, description: 'Request status retrieved' })
  async checkManagementStatus(
    @Param('correlationId') correlationId: string,
    @Request() req: any,
  ) {
    const user = req.user;
    
    const status = await this.requestTracker.getRequestStatus(
      correlationId, 
      'management-center', 
      user.userId
    );

    if (!status) {
      return ApiResponseDto.error(
        `Request not found for correlation ID: ${correlationId}`, 
        404
      );
    }

    return ApiResponseDto.success(status, 'Request status retrieved');
  }

  @Get('')
  @ApiOperation({ 
    summary: 'Get all management-center requests for user',
    description: 'List all management-center requests for the current user'
  })
  @ApiQuery({ name: 'active', required: false, description: 'Filter for active requests only' })
  @ApiResponse({ status: 200, description: 'Management center requests retrieved' })
  async getManagementRequests(
    @Query('active') activeOnly: string,
    @Request() req: any,
  ) {
    const user = req.user;
    
    const requests = await this.requestTracker.getDomainRequests('management-center', user.userId);
    
    const filteredRequests = activeOnly === 'true' 
      ? requests.filter(r => !['completed', 'error'].includes(r.status))
      : requests;

    return ApiResponseDto.success({
      domain: 'management-center',
      requests: filteredRequests,
      total: filteredRequests.length,
      active: filteredRequests.filter(r => !['completed', 'error'].includes(r.status)).length,
    }, 'Management center requests retrieved');
  }
}
