"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UserEmailVerifiedHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserEmailVerifiedHandler = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
const event_bus_1 = require("../../../../../../libs/event-bus/src");
const event_store_1 = require("../../../../../../libs/event-store/src");
const user_email_verified_event_1 = require("../user-email-verified.event");
let UserEmailVerifiedHandler = UserEmailVerifiedHandler_1 = class UserEmailVerifiedHandler {
    constructor(eventBusService, eventStoreService) {
        this.eventBusService = eventBusService;
        this.eventStoreService = eventStoreService;
        this.logger = new common_1.Logger(UserEmailVerifiedHandler_1.name);
    }
    async handle(event) {
        this.logger.log(`Handling UserEmailVerifiedEvent for user: ${event.userId}`);
        try {
            await this.eventStoreService.appendToStream(`user-${event.userId}`, [event], -1);
            await this.eventBusService.publish(event);
            this.logger.log(`Successfully processed UserEmailVerifiedEvent for user: ${event.userId}`);
        }
        catch (error) {
            this.logger.error(`Failed to process UserEmailVerifiedEvent for user: ${event.userId}`, error);
            throw error;
        }
    }
};
exports.UserEmailVerifiedHandler = UserEmailVerifiedHandler;
exports.UserEmailVerifiedHandler = UserEmailVerifiedHandler = UserEmailVerifiedHandler_1 = __decorate([
    (0, cqrs_1.EventsHandler)(user_email_verified_event_1.UserEmailVerifiedEvent),
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [event_bus_1.EventBusService,
        event_store_1.EventStoreService])
], UserEmailVerifiedHandler);
//# sourceMappingURL=user-email-verified.handler.js.map