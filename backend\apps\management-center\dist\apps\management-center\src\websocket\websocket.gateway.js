"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WebSocketGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
Object.defineProperty(exports, "WebSocketGateway", { enumerable: true, get: function () { return websockets_1.WebSocketGateway; } });
const socket_io_1 = require("socket.io");
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const ws_jwt_guard_1 = require("./guards/ws-jwt.guard");
let WebSocketGateway = WebSocketGateway_1 = class WebSocketGateway {
    constructor(jwtService) {
        this.jwtService = jwtService;
        this.logger = new common_1.Logger(WebSocketGateway_1.name);
        this.connectedClients = new Map();
    }
    async handleConnection(client) {
        try {
            const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
            if (!token) {
                this.logger.warn(`Client ${client.id} connected without token`);
                client.disconnect();
                return;
            }
            const payload = await this.jwtService.verifyAsync(token);
            const userId = payload.sub || payload.userId;
            if (!userId) {
                this.logger.warn(`Client ${client.id} connected with invalid token`);
                client.disconnect();
                return;
            }
            client.data.userId = userId;
            this.connectedClients.set(userId, client);
            this.logger.log(`Client ${client.id} connected for user ${userId}`);
            client.emit('connected', {
                message: 'WebSocket connection established',
                userId,
                timestamp: new Date(),
            });
        }
        catch (error) {
            this.logger.error(`Authentication failed for client ${client.id}:`, error.message);
            client.disconnect();
        }
    }
    handleDisconnect(client) {
        const userId = client.data?.userId;
        if (userId) {
            this.connectedClients.delete(userId);
            this.logger.log(`Client ${client.id} disconnected for user ${userId}`);
        }
        else {
            this.logger.log(`Client ${client.id} disconnected`);
        }
    }
    sendStatusToUser(userId, message) {
        const client = this.connectedClients.get(userId);
        if (client) {
            client.emit('request-status', message);
            this.logger.debug(`Sent status update to user ${userId}: ${message.status}`);
        }
        else {
            this.logger.warn(`User ${userId} not connected to WebSocket`);
        }
    }
    broadcastStatus(message) {
        this.server.emit('request-status', message);
        this.logger.debug(`Broadcasted status update: ${message.status}`);
    }
    handlePing(client) {
        return 'pong';
    }
    handleSubscribeRequest(data, client) {
        const userId = client.data.userId;
        client.join(`request-${data.requestId}`);
        this.logger.log(`User ${userId} subscribed to request ${data.requestId}`);
        return {
            event: 'subscribed',
            data: { requestId: data.requestId, message: 'Subscribed to request updates' },
        };
    }
    sendStatusToRequest(requestId, message) {
        this.server.to(`request-${requestId}`).emit('request-status', message);
        this.logger.debug(`Sent status update to request ${requestId}: ${message.status}`);
    }
    getConnectedClientsCount() {
        return this.connectedClients.size;
    }
    getConnectedUserIds() {
        return Array.from(this.connectedClients.keys());
    }
};
exports.WebSocketGateway = WebSocketGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], websockets_1.WebSocketGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('ping'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket]),
    __metadata("design:returntype", String)
], websockets_1.WebSocketGateway.prototype, "handlePing", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('subscribe-request'),
    (0, common_1.UseGuards)(ws_jwt_guard_1.WsJwtGuard),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], websockets_1.WebSocketGateway.prototype, "handleSubscribeRequest", null);
exports.WebSocketGateway = websockets_1.WebSocketGateway = WebSocketGateway_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: process.env.FRONTEND_URL || 'http://localhost:3001',
            credentials: true,
        },
        namespace: '/api-status',
    }),
    __metadata("design:paramtypes", [jwt_1.JwtService])
], websockets_1.WebSocketGateway);
//# sourceMappingURL=websocket.gateway.js.map