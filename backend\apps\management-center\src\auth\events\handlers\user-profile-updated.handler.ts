import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { Injectable, Logger } from '@nestjs/common';
import { EventBusService } from '@enterprise/event-bus';
import { EventStoreService } from '@enterprise/event-store';
import { UserProfileUpdatedEvent } from '../user-profile-updated.event';

@EventsHandler(UserProfileUpdatedEvent)
@Injectable()
export class UserProfileUpdatedHandler implements IEventHandler<UserProfileUpdatedEvent> {
  private readonly logger = new Logger(UserProfileUpdatedHandler.name);

  constructor(
    private readonly eventBusService: EventBusService,
    private readonly eventStoreService: EventStoreService,
  ) {}

  async handle(event: UserProfileUpdatedEvent) {
    this.logger.log(`Handling UserProfileUpdatedEvent for user: ${event.userId}`);

    try {
      // Store event in EventStore
      await this.eventStoreService.appendToStream(
        `user-${event.userId}`,
        [event],
        -1 // Expected version (-1 means any version)
      );

      // Publish to RabbitMQ for other services
      await this.eventBusService.publish(event);

      this.logger.log(`Successfully processed UserProfileUpdatedEvent for user: ${event.userId}`);
    } catch (error) {
      this.logger.error(`Failed to process UserProfileUpdatedEvent for user: ${event.userId}`, error);
      throw error;
    }
  }
}
