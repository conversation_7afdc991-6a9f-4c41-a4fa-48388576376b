"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserProfileUpdatedEvent = void 0;
class UserProfileUpdatedEvent extends common_1.DomainEvent {
    constructor(userId, email, updatedFields, updatedBy) {
        super('User', userId, 'UserProfileUpdated', 1);
        this.userId = userId;
        this.email = email;
        this.updatedFields = updatedFields;
        this.updatedBy = updatedBy;
    }
}
exports.UserProfileUpdatedEvent = UserProfileUpdatedEvent;
//# sourceMappingURL=user-profile-updated.event.js.map