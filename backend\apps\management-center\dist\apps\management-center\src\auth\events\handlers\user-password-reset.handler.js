"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UserPasswordResetHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPasswordResetHandler = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
const event_bus_1 = require("../../../../../../libs/event-bus/src");
const event_store_1 = require("../../../../../../libs/event-store/src");
const user_password_reset_event_1 = require("../user-password-reset.event");
let UserPasswordResetHandler = UserPasswordResetHandler_1 = class UserPasswordResetHandler {
    constructor(eventBusService, eventStoreService) {
        this.eventBusService = eventBusService;
        this.eventStoreService = eventStoreService;
        this.logger = new common_1.Logger(UserPasswordResetHandler_1.name);
    }
    async handle(event) {
        this.logger.log(`Handling UserPasswordResetEvent for user: ${event.userId}`);
        try {
            await this.eventStoreService.appendToStream(`user-${event.userId}`, [event], -1);
            await this.eventBusService.publish(event);
            this.logger.log(`Successfully processed UserPasswordResetEvent for user: ${event.userId}`);
        }
        catch (error) {
            this.logger.error(`Failed to process UserPasswordResetEvent for user: ${event.userId}`, error);
            throw error;
        }
    }
};
exports.UserPasswordResetHandler = UserPasswordResetHandler;
exports.UserPasswordResetHandler = UserPasswordResetHandler = UserPasswordResetHandler_1 = __decorate([
    (0, cqrs_1.EventsHandler)(user_password_reset_event_1.UserPasswordResetEvent),
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [event_bus_1.EventBusService,
        event_store_1.EventStoreService])
], UserPasswordResetHandler);
//# sourceMappingURL=user-password-reset.handler.js.map