"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EventBusService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventBusService = void 0;
const common_1 = require("@nestjs/common");
const rabbitmq_event_publisher_1 = require("./rabbitmq-event-publisher");
const rabbitmq_event_subscriber_1 = require("./rabbitmq-event-subscriber");
let EventBusService = EventBusService_1 = class EventBusService {
    constructor(publisher, subscriber) {
        this.publisher = publisher;
        this.subscriber = subscriber;
        this.logger = new common_1.Logger(EventBusService_1.name);
    }
    async publish(event) {
        try {
            await this.publisher.publish(event);
            this.logger.log(`Event ${event.eventType} published successfully`);
        }
        catch (error) {
            this.logger.error(`Failed to publish event ${event.eventType}:`, error);
            throw error;
        }
    }
    async publishBatch(events) {
        try {
            await this.publisher.publishBatch(events);
            this.logger.log(`Batch of ${events.length} events published successfully`);
        }
        catch (error) {
            this.logger.error(`Failed to publish batch of ${events.length} events:`, error);
            throw error;
        }
    }
    async subscribe(eventType, handler) {
        try {
            await this.subscriber.subscribe(eventType, handler);
            this.logger.log(`Subscribed to event ${eventType}`);
        }
        catch (error) {
            this.logger.error(`Failed to subscribe to event ${eventType}:`, error);
            throw error;
        }
    }
    async unsubscribe(eventType) {
        try {
            await this.subscriber.unsubscribe(eventType);
            this.logger.log(`Unsubscribed from event ${eventType}`);
        }
        catch (error) {
            this.logger.error(`Failed to unsubscribe from event ${eventType}:`, error);
            throw error;
        }
    }
};
exports.EventBusService = EventBusService;
exports.EventBusService = EventBusService = EventBusService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [rabbitmq_event_publisher_1.RabbitMQEventPublisher,
        rabbitmq_event_subscriber_1.RabbitMQEventSubscriber])
], EventBusService);
//# sourceMappingURL=event-bus.service.js.map