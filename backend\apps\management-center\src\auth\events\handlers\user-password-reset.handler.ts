import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { Injectable, Logger } from '@nestjs/common';
import { EventBusService } from '@enterprise/event-bus';
import { EventStoreService } from '@enterprise/event-store';
import { UserPasswordResetEvent } from '../user-password-reset.event';

@EventsHandler(UserPasswordResetEvent)
@Injectable()
export class UserPasswordResetHandler implements IEventHandler<UserPasswordResetEvent> {
  private readonly logger = new Logger(UserPasswordResetHandler.name);

  constructor(
    private readonly eventBusService: EventBusService,
    private readonly eventStoreService: EventStoreService,
  ) {}

  async handle(event: UserPasswordResetEvent) {
    this.logger.log(`Handling UserPasswordResetEvent for user: ${event.userId}`);

    try {
      // Store event in EventStore
      await this.eventStoreService.appendToStream(
        `user-${event.userId}`,
        [event],
        -1 // Expected version (-1 means any version)
      );

      // Publish to RabbitMQ for other services
      await this.eventBusService.publish(event);

      this.logger.log(`Successfully processed UserPasswordResetEvent for user: ${event.userId}`);
    } catch (error) {
      this.logger.error(`Failed to process UserPasswordResetEvent for user: ${event.userId}`, error);
      throw error;
    }
  }
}
