import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, Logger, UseGuards } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { WsJwtGuard } from './guards/ws-jwt.guard';

export interface RequestStatusMessage {
  requestId: string;
  status: 'received' | 'authenticated' | 'processing' | 'completed' | 'error';
  message: string;
  timestamp: Date;
  data?: any;
  error?: string;
}

@Injectable()
@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3001',
    credentials: true,
  },
  namespace: '/api-status',
})
export class WebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WebSocketGateway.name);
  private connectedClients = new Map<string, Socket>();

  constructor(private readonly jwtService: JwtService) {}

  async handleConnection(client: Socket) {
    try {
      // Extract token from handshake auth
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
      
      if (!token) {
        this.logger.warn(`Client ${client.id} connected without token`);
        client.disconnect();
        return;
      }

      // Verify JWT token
      const payload = await this.jwtService.verifyAsync(token);
      const userId = payload.sub || payload.userId;

      if (!userId) {
        this.logger.warn(`Client ${client.id} connected with invalid token`);
        client.disconnect();
        return;
      }

      // Store client with user ID
      client.data.userId = userId;
      this.connectedClients.set(userId, client);

      this.logger.log(`Client ${client.id} connected for user ${userId}`);
      
      // Send connection confirmation
      client.emit('connected', {
        message: 'WebSocket connection established',
        userId,
        timestamp: new Date(),
      });

    } catch (error) {
      this.logger.error(`Authentication failed for client ${client.id}:`, error.message);
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    const userId = client.data?.userId;
    if (userId) {
      this.connectedClients.delete(userId);
      this.logger.log(`Client ${client.id} disconnected for user ${userId}`);
    } else {
      this.logger.log(`Client ${client.id} disconnected`);
    }
  }

  // Send status update to specific user
  sendStatusToUser(userId: string, message: RequestStatusMessage) {
    const client = this.connectedClients.get(userId);
    if (client) {
      client.emit('request-status', message);
      this.logger.debug(`Sent status update to user ${userId}: ${message.status}`);
    } else {
      this.logger.warn(`User ${userId} not connected to WebSocket`);
    }
  }

  // Send any event to specific user (unified approach)
  sendToUser(userId: string, event: string, data: any): boolean {
    const client = this.connectedClients.get(userId);
    if (client) {
      client.emit(event, data);
      this.logger.debug(`Sent ${event} to user ${userId}`);
      return true;
    } else {
      this.logger.debug(`User ${userId} not connected to WebSocket`);
      return false;
    }
  }

  // Send status update to all connected clients (for admin notifications)
  broadcastStatus(message: RequestStatusMessage) {
    this.server.emit('request-status', message);
    this.logger.debug(`Broadcasted status update: ${message.status}`);
  }

  // Handle client ping for connection health check
  @SubscribeMessage('ping')
  handlePing(@ConnectedSocket() client: Socket): string {
    return 'pong';
  }

  // Handle client subscription to specific request updates
  @SubscribeMessage('subscribe-request')
  @UseGuards(WsJwtGuard)
  handleSubscribeRequest(
    @MessageBody() data: { requestId: string },
    @ConnectedSocket() client: Socket,
  ) {
    const userId = client.data.userId;
    client.join(`request-${data.requestId}`);
    this.logger.log(`User ${userId} subscribed to request ${data.requestId}`);
    
    return {
      event: 'subscribed',
      data: { requestId: data.requestId, message: 'Subscribed to request updates' },
    };
  }

  // Send status update to specific request subscribers
  sendStatusToRequest(requestId: string, message: RequestStatusMessage) {
    this.server.to(`request-${requestId}`).emit('request-status', message);
    this.logger.debug(`Sent status update to request ${requestId}: ${message.status}`);
  }

  // Get connected clients count
  getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  // Get connected user IDs
  getConnectedUserIds(): string[] {
    return Array.from(this.connectedClients.keys());
  }
}
