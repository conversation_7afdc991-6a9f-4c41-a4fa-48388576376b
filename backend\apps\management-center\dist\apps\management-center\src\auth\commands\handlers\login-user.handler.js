"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginUserHandler = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const bcrypt = require("bcrypt");
const uuid_1 = require("uuid");
const login_user_command_1 = require("../login-user.command");
const user_schema_1 = require("../../schemas/user.schema");
const refresh_token_schema_1 = require("../../schemas/refresh-token.schema");
const user_logged_in_event_1 = require("../../events/user-logged-in.event");
let LoginUserHandler = class LoginUserHandler {
    constructor(userWriteModel, userReadModel, refreshTokenModel, eventBus) {
        this.userWriteModel = userWriteModel;
        this.userReadModel = userReadModel;
        this.refreshTokenModel = refreshTokenModel;
        this.eventBus = eventBus;
    }
    async execute(command) {
        const { loginDto, userAgent, ipAddress } = command;
        const user = await this.userReadModel.findOne({ email: loginDto.email }).exec();
        if (!user) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const isPasswordValid = await bcrypt.compare(loginDto.password, user.password);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        if (!user.isActive()) {
            throw new common_1.UnauthorizedException('Account is not active or email not verified');
        }
        await this.userWriteModel.findByIdAndUpdate(user._id, { lastLoginAt: new Date() }, { new: true }).exec();
        const refreshToken = await this.generateRefreshToken(user, userAgent, ipAddress);
        const event = new user_logged_in_event_1.UserLoggedInEvent(user._id.toString(), user.email, ipAddress, userAgent, new Date());
        this.eventBus.publish(event);
        return { user, refreshToken };
    }
    async generateRefreshToken(user, userAgent, ipAddress) {
        const token = (0, uuid_1.v4)();
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 7);
        const refreshToken = new this.refreshTokenModel({
            token,
            userId: user._id,
            expiresAt,
            userAgent,
            ipAddress,
        });
        return refreshToken.save();
    }
};
exports.LoginUserHandler = LoginUserHandler;
exports.LoginUserHandler = LoginUserHandler = __decorate([
    (0, cqrs_1.CommandHandler)(login_user_command_1.LoginUserCommand),
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name, 'write')),
    __param(1, (0, mongoose_1.InjectModel)(user_schema_1.User.name, 'read')),
    __param(2, (0, mongoose_1.InjectModel)(refresh_token_schema_1.RefreshToken.name, 'write')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        cqrs_1.EventBus])
], LoginUserHandler);
//# sourceMappingURL=login-user.handler.js.map