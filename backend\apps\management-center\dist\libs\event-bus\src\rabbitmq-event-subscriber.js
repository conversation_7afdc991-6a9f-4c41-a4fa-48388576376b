"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RabbitMQEventSubscriber_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RabbitMQEventSubscriber = void 0;
const common_1 = require("@nestjs/common");
const amqp = require("amqplib");
let RabbitMQEventSubscriber = RabbitMQEventSubscriber_1 = class RabbitMQEventSubscriber {
    constructor(config) {
        this.config = config;
        this.logger = new common_1.Logger(RabbitMQEventSubscriber_1.name);
        this.isConnected = false;
        this.subscriptions = new Map();
    }
    async onModuleInit() {
        await this.connect();
    }
    async onModuleDestroy() {
        await this.disconnect();
    }
    async connect() {
        try {
            this.connection = await amqp.connect(this.config.url);
            this.channel = await this.connection.createChannel();
            await this.channel.assertExchange(this.config.exchange, 'topic', {
                durable: true,
            });
            this.isConnected = true;
            this.logger.log(`Connected to RabbitMQ at ${this.config.url}`);
            this.connection.on('error', (err) => {
                this.logger.error('RabbitMQ connection error:', err);
                this.isConnected = false;
            });
            this.connection.on('close', () => {
                this.logger.warn('RabbitMQ connection closed');
                this.isConnected = false;
            });
        }
        catch (error) {
            this.logger.error('Failed to connect to RabbitMQ:', error);
            throw error;
        }
    }
    async disconnect() {
        try {
            if (this.channel) {
                await this.channel.close();
            }
            if (this.connection) {
                await this.connection.close();
            }
            this.isConnected = false;
            this.logger.log('Disconnected from RabbitMQ');
        }
        catch (error) {
            this.logger.error('Error disconnecting from RabbitMQ:', error);
        }
    }
    async subscribe(eventType, handler) {
        if (!this.isConnected) {
            await this.connect();
        }
        try {
            const queueName = `${this.config.serviceName}.${eventType}`;
            const routingKey = `*.${eventType}`;
            await this.channel.assertQueue(queueName, {
                durable: true,
            });
            await this.channel.bindQueue(queueName, this.config.exchange, routingKey);
            await this.channel.consume(queueName, async (msg) => {
                if (msg) {
                    try {
                        const event = JSON.parse(msg.content.toString());
                        this.logger.log(`Received event ${event.eventType} for aggregate ${event.aggregateId}`);
                        await handler(event);
                        this.channel.ack(msg);
                        this.logger.log(`Successfully processed event ${event.eventType}`);
                    }
                    catch (error) {
                        this.logger.error(`Error processing event:`, error);
                        this.channel.nack(msg, false, true);
                    }
                }
            }, {
                noAck: false,
            });
            this.subscriptions.set(eventType, handler);
            this.logger.log(`Subscribed to event ${eventType} with routing key ${routingKey}`);
        }
        catch (error) {
            this.logger.error(`Failed to subscribe to event ${eventType}:`, error);
            throw error;
        }
    }
    async unsubscribe(eventType) {
        try {
            const queueName = `${this.config.serviceName}.${eventType}`;
            await this.channel.cancel(queueName);
            this.subscriptions.delete(eventType);
            this.logger.log(`Unsubscribed from event ${eventType}`);
        }
        catch (error) {
            this.logger.error(`Failed to unsubscribe from event ${eventType}:`, error);
            throw error;
        }
    }
};
exports.RabbitMQEventSubscriber = RabbitMQEventSubscriber;
exports.RabbitMQEventSubscriber = RabbitMQEventSubscriber = RabbitMQEventSubscriber_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Object])
], RabbitMQEventSubscriber);
//# sourceMappingURL=rabbitmq-event-subscriber.js.map