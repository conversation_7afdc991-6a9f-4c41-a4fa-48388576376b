{"version": 3, "file": "user.schema.js", "sourceRoot": "", "sources": ["../../../../../../src/auth/schemas/user.schema.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAA+D;AAC/D,uCAA2C;AAE3C,IAAY,QAKX;AALD,WAAY,QAAQ;IAClB,uCAA2B,CAAA;IAC3B,2BAAe,CAAA;IACf,+BAAmB,CAAA;IACnB,yBAAa,CAAA;AACf,CAAC,EALW,QAAQ,wBAAR,QAAQ,QAKnB;AAED,IAAY,UAKX;AALD,WAAY,UAAU;IACpB,+BAAiB,CAAA;IACjB,mCAAqB,CAAA;IACrB,qCAAuB,CAAA;IACvB,iCAAmB,CAAA;AACrB,CAAC,EALW,UAAU,0BAAV,UAAU,QAKrB;AAQM,IAAM,IAAI,GAAV,MAAM,IAAI;IAqDf,IAAI,QAAQ;QACV,OAAO,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC9C,CAAC;IAGD,QAAQ;QACN,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC;IACjE,CAAC;IAGD,OAAO,CAAC,IAAc;QACpB,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;IAC5B,CAAC;IAGD,cAAc,CAAC,IAAc;QAC3B,MAAM,aAAa,GAAG;YACpB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAClB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACrB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACnB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;SAC1B,CAAC;QACF,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;CACF,CAAA;AA7EY,oBAAI;AAEf;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;mCACzB;AAGd;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sCACR;AAGjB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uCACP;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sCACR;AAGjB;IADC,IAAA,eAAI,GAAE;;mCACQ;AAGf;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC;;kCAClC;AAGf;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,OAAO,EAAE,CAAC;;oCACrC;AAGnB;IADC,IAAA,eAAI,GAAE;8BACO,IAAI;yCAAC;AAGnB;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;2CACF;AAGvB;IADC,IAAA,eAAI,GAAE;;oDACyB;AAGhC;IADC,IAAA,eAAI,GAAE;8BACoB,IAAI;sDAAC;AAGhC;IADC,IAAA,eAAI,GAAE;;gDACqB;AAG5B;IADC,IAAA,eAAI,GAAE;8BACgB,IAAI;kDAAC;AAG5B;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,gBAAK,CAAC,QAAQ,EAAE,CAAC;8BACd,gBAAK,CAAC,QAAQ;4CAAC;AAGhC;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;8BACjB,IAAI;uCAAC;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;8BACjB,IAAI;uCAAC;AAGhB;IADC,IAAA,eAAI,GAAE;8BACK,IAAI;uCAAC;eAlDN,IAAI;IAJhB,IAAA,iBAAM,EAAC;QACN,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,OAAO;KACpB,CAAC;GACW,IAAI,CA6EhB;AAEY,QAAA,UAAU,GAAG,wBAAa,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAG7D,kBAAU,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AACjD,kBAAU,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC,CAAC;AACxC,kBAAU,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AAChC,kBAAU,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9B,kBAAU,CAAC,KAAK,CAAC,EAAE,sBAAsB,EAAE,CAAC,EAAE,CAAC,CAAC;AAChD,kBAAU,CAAC,KAAK,CAAC,EAAE,kBAAkB,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5C,kBAAU,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;AAGnC,kBAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC;IACjC,OAAO,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC9C,CAAC,CAAC,CAAC;AAGH,kBAAU,CAAC,GAAG,CAAC,QAAQ,EAAE;IACvB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,UAAS,GAAG,EAAE,GAAG;QAC1B,OAAO,GAAG,CAAC,QAAQ,CAAC;QACpB,OAAO,GAAG,CAAC,sBAAsB,CAAC;QAClC,OAAO,GAAG,CAAC,kBAAkB,CAAC;QAC9B,OAAO,GAAG,CAAC;IACb,CAAC;CACF,CAAC,CAAC"}