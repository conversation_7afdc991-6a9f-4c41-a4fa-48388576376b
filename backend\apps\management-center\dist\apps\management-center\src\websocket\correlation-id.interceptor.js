"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CorrelationIdInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorrelationIdInterceptor = exports.UnifiedTracking = exports.UNIFIED_TRACKING_KEY = void 0;
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const unified_request_tracker_service_1 = require("./unified-request-tracker.service");
const core_1 = require("@nestjs/core");
const uuid_1 = require("uuid");
exports.UNIFIED_TRACKING_KEY = 'unified_tracking';
const UnifiedTracking = (operation) => {
    return (target, propertyKey, descriptor) => {
        Reflect.defineMetadata(exports.UNIFIED_TRACKING_KEY, operation || true, descriptor.value);
    };
};
exports.UnifiedTracking = UnifiedTracking;
let CorrelationIdInterceptor = CorrelationIdInterceptor_1 = class CorrelationIdInterceptor {
    constructor(requestTracker, reflector) {
        this.requestTracker = requestTracker;
        this.reflector = reflector;
        this.logger = new common_1.Logger(CorrelationIdInterceptor_1.name);
    }
    intercept(context, next) {
        const isEnabled = this.reflector.getAllAndOverride(exports.UNIFIED_TRACKING_KEY, [context.getHandler(), context.getClass()]);
        if (!isEnabled) {
            return next.handle();
        }
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        const user = request.user;
        if (!user || !user.userId) {
            return next.handle();
        }
        let correlationId = request.headers['x-correlation-id'] ||
            request.headers['correlation-id'];
        if (!correlationId) {
            correlationId = (0, uuid_1.v4)();
            this.logger.warn(`No correlation ID provided, generated: ${correlationId}`);
        }
        const endpoint = request.route?.path || request.url;
        const method = request.method;
        const domain = this.extractDomain(request);
        const operation = typeof isEnabled === 'string' ? isEnabled : undefined;
        this.logger.debug(`Processing request with correlation ID: ${correlationId}`);
        request.correlationId = correlationId;
        const handleInitialRequest = async () => {
            try {
                const initialResponse = await this.requestTracker.handleInitialRequest(correlationId, user.userId, domain, endpoint, method);
                response.setHeader('X-Correlation-ID', correlationId);
                return initialResponse;
            }
            catch (error) {
                this.logger.error(`Failed to handle initial request: ${error.message}`);
                throw error;
            }
        };
        return new rxjs_1.Observable(subscriber => {
            handleInitialRequest().then(initialResponse => {
                this.requestTracker.setProcessing(correlationId, operation);
                next.handle().pipe((0, rxjs_1.tap)(async (result) => {
                    await this.requestTracker.completeRequest(correlationId, {
                        success: true,
                        data: result,
                        endpoint,
                        method,
                    });
                }), (0, rxjs_1.catchError)(async (error) => {
                    await this.requestTracker.failRequest(correlationId, error.message || 'Request processing failed');
                    return (0, rxjs_1.throwError)(() => error);
                })).subscribe({
                    next: (result) => {
                        subscriber.next({
                            ...initialResponse,
                            message: 'Request received and validated. Processing in background.',
                            processingInBackground: true,
                        });
                        subscriber.complete();
                    },
                    error: (error) => {
                        subscriber.error(error);
                    }
                });
            }).catch(error => {
                subscriber.error(error);
            });
        });
    }
    extractDomain(request) {
        const path = request.url || '';
        if (path.includes('/users') || path.includes('/organizations') || path.includes('/settings')) {
            return 'management-center';
        }
        if (path.includes('/correspondence') || path.includes('/messages')) {
            return 'correspondence';
        }
        const domain = request.headers['x-service-domain'];
        if (domain) {
            return domain;
        }
        return 'management-center';
    }
};
exports.CorrelationIdInterceptor = CorrelationIdInterceptor;
exports.CorrelationIdInterceptor = CorrelationIdInterceptor = CorrelationIdInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [unified_request_tracker_service_1.UnifiedRequestTrackerService,
        core_1.Reflector])
], CorrelationIdInterceptor);
//# sourceMappingURL=correlation-id.interceptor.js.map