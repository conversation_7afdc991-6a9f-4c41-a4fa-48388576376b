# Environment Configuration
NODE_ENV=development

# API Gateway
GATEWAY_PORT=3000
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Management Center Service
MANAGEMENT_SERVICE_HOST=localhost
MANAGEMENT_SERVICE_PORT=3001
MANAGEMENT_HTTP_PORT=4001

# Correspondence Service
CORRESPONDENCE_SERVICE_HOST=localhost
CORRESPONDENCE_SERVICE_PORT=3002
CORRESPONDENCE_HTTP_PORT=4002

# Database Configuration - Management Center (PostgreSQL - Legacy)
MANAGEMENT_DB_HOST=localhost
MANAGEMENT_DB_PORT=5432
MANAGEMENT_DB_USERNAME=postgres
MANAGEMENT_DB_PASSWORD=password
MANAGEMENT_DB_NAME=management_center

# MongoDB Configuration - Management Center
MONGODB_WRITE_URI=*********************************************************************************
MONGODB_READ_URI=********************************************************************************

# Database Configuration - Correspondence
CORRESPONDENCE_DB_HOST=localhost
CORRESPONDENCE_DB_PORT=5433
CORRESPONDENCE_DB_USERNAME=postgres
CORRESPONDENCE_DB_PASSWORD=password
CORRESPONDENCE_DB_NAME=correspondence

# EventStore Configuration
EVENTSTORE_CONNECTION_STRING=esdb://localhost:2113?tls=false
EVENTSTORE_USERNAME=admin
EVENTSTORE_PASSWORD=changeit

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# RabbitMQ Configuration
RABBITMQ_URL=amqp://admin:password@localhost:5672
RABBITMQ_EXCHANGE=enterprise.events
RABBITMQ_MANAGEMENT_URL=http://localhost:15672

# Email Configuration (for correspondence service)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png

# Logging
LOG_LEVEL=debug
LOG_FORMAT=json
