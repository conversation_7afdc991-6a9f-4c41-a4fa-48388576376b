import { Injectable, Logger } from '@nestjs/common';
import { RequestStatusMessage } from './websocket.gateway';

export interface RequestStatus {
  requestId: string;
  userId: string;
  endpoint: string;
  method: string;
  status: 'received' | 'authenticated' | 'processing' | 'completed' | 'error';
  message: string;
  startTime: Date;
  lastUpdated: Date;
  data?: any;
  error?: string;
  progress?: number; // 0-100
}

@Injectable()
export class RequestStatusService {
  private readonly logger = new Logger(RequestStatusService.name);
  private requestStatuses = new Map<string, RequestStatus>();
  
  // TTL for completed/error requests (in milliseconds)
  private readonly TTL_COMPLETED = 5 * 60 * 1000; // 5 minutes
  private readonly TTL_ACTIVE = 30 * 60 * 1000; // 30 minutes

  constructor() {
    // Clean up old requests every minute
    setInterval(() => this.cleanupOldRequests(), 60 * 1000);
  }

  // Create a new request status
  createRequestStatus(
    requestId: string,
    userId: string,
    endpoint: string,
    method: string,
  ): RequestStatus {
    const status: RequestStatus = {
      requestId,
      userId,
      endpoint,
      method,
      status: 'received',
      message: `Request received: ${method} ${endpoint}`,
      startTime: new Date(),
      lastUpdated: new Date(),
      progress: 0,
    };

    this.requestStatuses.set(requestId, status);
    this.logger.debug(`Created request status for ${requestId}`);
    
    return status;
  }

  // Update request status
  updateRequestStatus(
    requestId: string,
    status: RequestStatus['status'],
    message: string,
    data?: any,
    error?: string,
    progress?: number,
  ): RequestStatus | null {
    const requestStatus = this.requestStatuses.get(requestId);
    if (!requestStatus) {
      this.logger.warn(`Request status not found for ID: ${requestId}`);
      return null;
    }

    requestStatus.status = status;
    requestStatus.message = message;
    requestStatus.lastUpdated = new Date();
    
    if (data !== undefined) {
      requestStatus.data = data;
    }
    
    if (error !== undefined) {
      requestStatus.error = error;
    }
    
    if (progress !== undefined) {
      requestStatus.progress = progress;
    }

    this.requestStatuses.set(requestId, requestStatus);
    this.logger.debug(`Updated request status ${requestId}: ${status}`);
    
    return requestStatus;
  }

  // Get request status by ID
  getRequestStatus(requestId: string): RequestStatus | null {
    return this.requestStatuses.get(requestId) || null;
  }

  // Get all request statuses for a user
  getUserRequestStatuses(userId: string): RequestStatus[] {
    return Array.from(this.requestStatuses.values()).filter(
      status => status.userId === userId
    );
  }

  // Get active (non-completed) requests for a user
  getUserActiveRequests(userId: string): RequestStatus[] {
    return this.getUserRequestStatuses(userId).filter(
      status => !['completed', 'error'].includes(status.status)
    );
  }

  // Mark request as completed
  completeRequest(requestId: string, data?: any): RequestStatus | null {
    return this.updateRequestStatus(
      requestId,
      'completed',
      'Request completed successfully',
      data,
      undefined,
      100
    );
  }

  // Mark request as failed
  failRequest(requestId: string, error: string): RequestStatus | null {
    return this.updateRequestStatus(
      requestId,
      'error',
      'Request failed',
      undefined,
      error,
      undefined
    );
  }

  // Set authentication passed
  setAuthenticated(requestId: string, userInfo?: any): RequestStatus | null {
    return this.updateRequestStatus(
      requestId,
      'authenticated',
      'Authentication passed',
      userInfo,
      undefined,
      25
    );
  }

  // Set processing started
  setProcessing(requestId: string, operation?: string, progress?: number): RequestStatus | null {
    const message = operation 
      ? `Processing: ${operation}`
      : 'Server is processing the request';
    
    return this.updateRequestStatus(
      requestId,
      'processing',
      message,
      undefined,
      undefined,
      progress || 50
    );
  }

  // Delete request status (for cleanup)
  deleteRequestStatus(requestId: string): boolean {
    const deleted = this.requestStatuses.delete(requestId);
    if (deleted) {
      this.logger.debug(`Deleted request status ${requestId}`);
    }
    return deleted;
  }

  // Clean up old requests
  private cleanupOldRequests(): void {
    const now = new Date().getTime();
    let cleanedCount = 0;

    for (const [requestId, status] of this.requestStatuses.entries()) {
      const age = now - status.lastUpdated.getTime();
      const isCompleted = ['completed', 'error'].includes(status.status);
      
      const shouldCleanup = isCompleted 
        ? age > this.TTL_COMPLETED 
        : age > this.TTL_ACTIVE;

      if (shouldCleanup) {
        this.requestStatuses.delete(requestId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug(`Cleaned up ${cleanedCount} old request statuses`);
    }
  }

  // Get service statistics
  getStatistics() {
    const statuses = Array.from(this.requestStatuses.values());
    
    return {
      totalRequests: statuses.length,
      activeRequests: statuses.filter(s => !['completed', 'error'].includes(s.status)).length,
      completedRequests: statuses.filter(s => s.status === 'completed').length,
      errorRequests: statuses.filter(s => s.status === 'error').length,
      statusBreakdown: {
        received: statuses.filter(s => s.status === 'received').length,
        authenticated: statuses.filter(s => s.status === 'authenticated').length,
        processing: statuses.filter(s => s.status === 'processing').length,
        completed: statuses.filter(s => s.status === 'completed').length,
        error: statuses.filter(s => s.status === 'error').length,
      }
    };
  }
}
