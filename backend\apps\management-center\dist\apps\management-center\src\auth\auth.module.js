"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const mongoose_1 = require("@nestjs/mongoose");
const cqrs_1 = require("@nestjs/cqrs");
const auth_controller_1 = require("./auth.controller");
const auth_service_1 = require("./auth.service");
const jwt_strategy_1 = require("./strategies/jwt.strategy");
const local_strategy_1 = require("./strategies/local.strategy");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const local_auth_guard_1 = require("./guards/local-auth.guard");
const roles_guard_1 = require("./guards/roles.guard");
const user_schema_1 = require("./schemas/user.schema");
const refresh_token_schema_1 = require("./schemas/refresh-token.schema");
const register_user_handler_1 = require("./commands/handlers/register-user.handler");
const login_user_handler_1 = require("./commands/handlers/login-user.handler");
const user_registered_handler_1 = require("./events/handlers/user-registered.handler");
const user_logged_in_handler_1 = require("./events/handlers/user-logged-in.handler");
const user_profile_updated_handler_1 = require("./events/handlers/user-profile-updated.handler");
const user_email_verified_handler_1 = require("./events/handlers/user-email-verified.handler");
const user_password_reset_handler_1 = require("./events/handlers/user-password-reset.handler");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            passport_1.PassportModule,
            jwt_1.JwtModule.register({
                secret: process.env.JWT_SECRET || 'your-secret-key',
                signOptions: { expiresIn: '24h' },
            }),
            mongoose_1.MongooseModule.forFeature([
                { name: user_schema_1.User.name, schema: user_schema_1.UserSchema, connectionName: 'write' },
                { name: refresh_token_schema_1.RefreshToken.name, schema: refresh_token_schema_1.RefreshTokenSchema, connectionName: 'write' },
            ]),
            mongoose_1.MongooseModule.forFeature([
                { name: user_schema_1.User.name, schema: user_schema_1.UserSchema, connectionName: 'read' },
                { name: refresh_token_schema_1.RefreshToken.name, schema: refresh_token_schema_1.RefreshTokenSchema, connectionName: 'read' },
            ]),
            cqrs_1.CqrsModule,
        ],
        controllers: [auth_controller_1.AuthController],
        providers: [
            auth_service_1.AuthService,
            jwt_strategy_1.JwtStrategy,
            local_strategy_1.LocalStrategy,
            jwt_auth_guard_1.JwtAuthGuard,
            local_auth_guard_1.LocalAuthGuard,
            roles_guard_1.RolesGuard,
            register_user_handler_1.RegisterUserHandler,
            login_user_handler_1.LoginUserHandler,
            user_registered_handler_1.UserRegisteredHandler,
            user_logged_in_handler_1.UserLoggedInHandler,
            user_profile_updated_handler_1.UserProfileUpdatedHandler,
            user_email_verified_handler_1.UserEmailVerifiedHandler,
            user_password_reset_handler_1.UserPasswordResetHandler,
        ],
        exports: [auth_service_1.AuthService, jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard],
    })
], AuthModule);
//# sourceMappingURL=auth.module.js.map