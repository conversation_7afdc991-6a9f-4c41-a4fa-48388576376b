"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UserCreatedHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserCreatedHandler = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
const user_created_event_1 = require("../events/user-created.event");
const event_store_1 = require("../../../../../libs/event-store/src");
const uuid_1 = require("uuid");
let UserCreatedHandler = UserCreatedHandler_1 = class UserCreatedHandler {
    constructor(eventStore) {
        this.eventStore = eventStore;
        this.logger = new common_1.Logger(UserCreatedHandler_1.name);
    }
    async handle(event) {
        this.logger.log(`Handling UserCreatedEvent for user: ${event.userId}`);
        const domainEvent = {
            eventId: (0, uuid_1.v4)(),
            eventType: 'UserCreated',
            aggregateId: event.userId,
            aggregateType: 'User',
            eventVersion: 1,
            eventData: {
                userId: event.userId,
                email: event.email,
                firstName: event.firstName,
                lastName: event.lastName,
                role: event.role,
                organizationId: event.organizationId,
            },
            metadata: {
                source: 'management-center',
                timestamp: new Date().toISOString(),
            },
            occurredAt: new Date(),
        };
        try {
            await this.eventStore.saveEvents(event.userId, [domainEvent], -1);
            this.logger.log(`UserCreated event saved to EventStore for user: ${event.userId}`);
        }
        catch (error) {
            this.logger.error(`Failed to save UserCreated event to EventStore:`, error);
        }
    }
};
exports.UserCreatedHandler = UserCreatedHandler;
exports.UserCreatedHandler = UserCreatedHandler = UserCreatedHandler_1 = __decorate([
    (0, cqrs_1.EventsHandler)(user_created_event_1.UserCreatedEvent),
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [event_store_1.EventStoreService])
], UserCreatedHandler);
//# sourceMappingURL=user-created.handler.js.map