"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EventStoreService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventStoreService = void 0;
const common_1 = require("@nestjs/common");
const db_client_1 = require("@eventstore/db-client");
const uuid_1 = require("uuid");
let EventStoreService = EventStoreService_1 = class EventStoreService {
    constructor() {
        this.logger = new common_1.Logger(EventStoreService_1.name);
        this.client = db_client_1.EventStoreDBClient.connectionString(process.env.EVENTSTORE_CONNECTION_STRING || 'esdb://localhost:2113?tls=false');
    }
    async saveEvents(aggregateId, events, expectedVersion) {
        try {
            const streamName = this.getStreamName(aggregateId);
            const eventData = events.map(event => (0, db_client_1.jsonEvent)({
                id: (0, uuid_1.v4)(),
                type: event.eventType,
                data: {
                    ...event.eventData,
                    aggregateId: event.aggregateId,
                    aggregateType: event.aggregateType,
                    eventVersion: event.eventVersion,
                    occurredAt: event.occurredAt,
                    metadata: event.metadata
                }
            }));
            await this.client.appendToStream(streamName, eventData, {
                expectedRevision: expectedVersion === -1 ? 'no_stream' : BigInt(expectedVersion)
            });
            this.logger.log(`Saved ${events.length} events for aggregate ${aggregateId}`);
        }
        catch (error) {
            this.logger.error(`Failed to save events for aggregate ${aggregateId}`, error);
            throw error;
        }
    }
    async getEvents(aggregateId, fromVersion) {
        try {
            const streamName = this.getStreamName(aggregateId);
            const events = [];
            const readStream = this.client.readStream(streamName, {
                direction: db_client_1.FORWARDS,
                fromRevision: fromVersion ? BigInt(fromVersion) : db_client_1.START
            });
            for await (const resolvedEvent of readStream) {
                if (resolvedEvent.event) {
                    const event = this.mapToDomainEvent(resolvedEvent.event);
                    events.push(event);
                }
            }
            return events;
        }
        catch (error) {
            this.logger.error(`Failed to get events for aggregate ${aggregateId}`, error);
            throw error;
        }
    }
    async getAllEvents(fromPosition) {
        try {
            const events = [];
            const readAll = this.client.readAll({
                direction: db_client_1.FORWARDS,
                fromPosition: fromPosition ? BigInt(fromPosition) : db_client_1.START
            });
            for await (const resolvedEvent of readAll) {
                if (resolvedEvent.event && !resolvedEvent.event.streamId.startsWith('$')) {
                    const event = this.mapToDomainEvent(resolvedEvent.event);
                    events.push(event);
                }
            }
            return events;
        }
        catch (error) {
            this.logger.error('Failed to get all events', error);
            throw error;
        }
    }
    getStreamName(aggregateId) {
        return `aggregate-${aggregateId}`;
    }
    mapToDomainEvent(eventData) {
        return {
            eventId: eventData.id,
            eventType: eventData.type,
            aggregateId: eventData.data.aggregateId,
            aggregateType: eventData.data.aggregateType,
            eventVersion: eventData.data.eventVersion,
            eventData: eventData.data,
            metadata: eventData.data.metadata,
            occurredAt: new Date(eventData.data.occurredAt)
        };
    }
};
exports.EventStoreService = EventStoreService;
exports.EventStoreService = EventStoreService = EventStoreService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], EventStoreService);
//# sourceMappingURL=event-store.service.js.map