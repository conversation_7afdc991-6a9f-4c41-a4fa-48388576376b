// MongoDB initialization script for Management Center
// This script creates the necessary databases and collections

// Switch to the write database
db = db.getSiblingDB('management_center_write');

// Create collections with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['email', 'password', 'firstName', 'lastName', 'role', 'status'],
      properties: {
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
          description: 'must be a valid email address'
        },
        password: {
          bsonType: 'string',
          minLength: 8,
          description: 'must be a string with at least 8 characters'
        },
        firstName: {
          bsonType: 'string',
          minLength: 1,
          description: 'must be a non-empty string'
        },
        lastName: {
          bsonType: 'string',
          minLength: 1,
          description: 'must be a non-empty string'
        },
        role: {
          bsonType: 'string',
          enum: ['user', 'manager', 'admin', 'super_admin'],
          description: 'must be one of the allowed user roles'
        },
        status: {
          bsonType: 'string',
          enum: ['active', 'inactive', 'suspended', 'pending'],
          description: 'must be one of the allowed user statuses'
        }
      }
    }
  }
});

db.createCollection('refresh_tokens', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['token', 'userId', 'expiresAt'],
      properties: {
        token: {
          bsonType: 'string',
          description: 'must be a string'
        },
        userId: {
          bsonType: 'objectId',
          description: 'must be a valid ObjectId'
        },
        expiresAt: {
          bsonType: 'date',
          description: 'must be a date'
        }
      }
    }
  }
});

// Create indexes for users collection
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ organizationId: 1 });
db.users.createIndex({ status: 1 });
db.users.createIndex({ role: 1 });
db.users.createIndex({ emailVerificationToken: 1 });
db.users.createIndex({ passwordResetToken: 1 });
db.users.createIndex({ createdAt: 1 });

// Create indexes for refresh_tokens collection
db.refresh_tokens.createIndex({ token: 1 }, { unique: true });
db.refresh_tokens.createIndex({ userId: 1 });
db.refresh_tokens.createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });
db.refresh_tokens.createIndex({ revoked: 1 });
db.refresh_tokens.createIndex({ createdAt: 1 });

print('Write database initialized successfully');

// Switch to the read database
db = db.getSiblingDB('management_center_read');

// Create the same collections for read database
db.createCollection('users');
db.createCollection('refresh_tokens');

// Create the same indexes for read database
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ organizationId: 1 });
db.users.createIndex({ status: 1 });
db.users.createIndex({ role: 1 });
db.users.createIndex({ emailVerificationToken: 1 });
db.users.createIndex({ passwordResetToken: 1 });
db.users.createIndex({ createdAt: 1 });

db.refresh_tokens.createIndex({ token: 1 }, { unique: true });
db.refresh_tokens.createIndex({ userId: 1 });
db.refresh_tokens.createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });
db.refresh_tokens.createIndex({ revoked: 1 });
db.refresh_tokens.createIndex({ createdAt: 1 });

print('Read database initialized successfully');

// Create a default super admin user (for development only)
if (db.getSiblingDB('management_center_write').users.countDocuments() === 0) {
  db.getSiblingDB('management_center_write').users.insertOne({
    email: '<EMAIL>',
    password: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uDfm', // password: admin123
    firstName: 'Super',
    lastName: 'Admin',
    role: 'super_admin',
    status: 'active',
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date()
  });
  
  print('Default super admin user created: <EMAIL> / admin123');
}
