# API Request Flow Process - Management Center Service

This document outlines the complete process flow for handling API requests in the Management Center Service.

## Architecture Overview

The Management Center Service is built with:
- **NestJS Framework** with hybrid HTTP + TCP microservice architecture
- **MongoDB** with separate read/write databases for CQRS pattern
- **PostgreSQL** for legacy data support
- **EventStore** for event sourcing
- **RabbitMQ** for event messaging
- **JWT Authentication** with role-based access control

## Complete Request Flow

### 1. Client Request
- Client sends HTTP request to API Gateway or directly to Management Center
- Request includes headers, body, and authentication token (if required)

### 2. API Gateway (Optional)
- If coming through API Gateway, request is proxied to Management Center
- JWT validation may occur at gateway level
- Request forwarding to appropriate microservice

### 3. NestJS Application Bootstrap
- Request hits the NestJS application running on port 4001 (HTTP) or 3001 (TCP)
- Express.js server handles HTTP requests
- TCP transport handles microservice communication

### 4. Middleware Pipeline
- **CORS Handling**: Cross-origin request validation
- **Body Parsing**: JSON/form data parsing
- **Request Logging**: Optional request logging middleware

### 5. Global Validation Pipe
- **ValidationPipe**: Validates request DTOs using class-validator
- **Whitelist**: Removes unknown properties
- **Transform**: Transforms plain objects to class instances
- **Validation**: Validates against DTO decorators

### 6. Route Resolution
- NestJS router matches request to controller method
- Route parameters and query parameters extracted
- Controller method identified

### 7. Guards Execution (Security Layer)
- **JwtAuthGuard**: Validates JWT token and extracts user
  - Checks for @Public() decorator to skip authentication
  - Validates token signature and expiration
  - Extracts user payload from token
- **RolesGuard**: Validates user permissions
  - Checks @Roles() decorator requirements
  - Validates user role hierarchy
  - Ensures sufficient permissions

### 8. Controller Method Execution
- Controller method receives validated request
- Dependency injection provides required services
- Business logic delegation to services

### 9. Service Layer Processing
- **CQRS Pattern**: Commands and Queries separation
- **Command Bus**: Handles write operations
- **Query Bus**: Handles read operations
- **Event Bus**: Publishes domain events

### 10. Database Operations
- **MongoDB Write**: Write operations to write database
- **MongoDB Read**: Read operations from read database
- **PostgreSQL**: Legacy data operations (if needed)
- **Transaction Management**: Ensures data consistency

### 11. Event Processing
- **Domain Events**: Published to EventStore
- **Event Handlers**: Process events asynchronously
- **RabbitMQ**: Distributes events to other services
- **Event Sourcing**: Maintains event history

### 12. Response Formation
- **Data Transformation**: Convert entities to DTOs
- **Response Wrapping**: Wrap in ApiResponseDto format
- **Status Code Setting**: Set appropriate HTTP status
- **Headers Addition**: Add response headers

### 13. Response Delivery
- Response sent back through middleware pipeline
- JSON serialization
- HTTP response transmission to client

## Example: User Creation Flow

```
POST /users
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "role": "USER"
}
```

### Detailed Steps:

1. **Request Reception**: NestJS receives POST request
2. **Validation**: ValidationPipe validates CreateUserDto
3. **Authentication**: JwtAuthGuard validates JWT token
4. **Authorization**: RolesGuard checks ADMIN/SUPER_ADMIN role
5. **Controller**: UsersController.create() method called
6. **Command**: CreateUserCommand dispatched via CommandBus
7. **Handler**: CreateUserHandler processes command
8. **Database**: User saved to MongoDB write database
9. **Event**: UserCreatedEvent published to EventStore
10. **Response**: Success response with created user data

## Error Handling

- **Validation Errors**: 400 Bad Request with validation details
- **Authentication Errors**: 401 Unauthorized
- **Authorization Errors**: 403 Forbidden
- **Not Found Errors**: 404 Not Found
- **Server Errors**: 500 Internal Server Error
- **Custom Exceptions**: Domain-specific error responses

## Performance Considerations

- **Database Separation**: Read/write database separation for performance
- **Caching**: MongoDB read replicas for query optimization
- **Event Async Processing**: Non-blocking event handling
- **Connection Pooling**: Efficient database connections
- **Microservice Communication**: TCP for internal service calls
