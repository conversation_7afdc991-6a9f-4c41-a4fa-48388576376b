"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EventStorePublisher_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventStorePublisher = void 0;
const common_1 = require("@nestjs/common");
const cqrs_1 = require("@nestjs/cqrs");
let EventStorePublisher = EventStorePublisher_1 = class EventStorePublisher {
    constructor(eventBus) {
        this.eventBus = eventBus;
        this.logger = new common_1.Logger(EventStorePublisher_1.name);
    }
    async publish(event) {
        try {
            this.eventBus.publish(event);
            this.logger.log(`Published event ${event.eventType} for aggregate ${event.aggregateId}`);
        }
        catch (error) {
            this.logger.error(`Failed to publish event ${event.eventType}`, error);
            throw error;
        }
    }
    async publishAll(events) {
        try {
            this.eventBus.publishAll(events);
            this.logger.log(`Published ${events.length} events`);
        }
        catch (error) {
            this.logger.error(`Failed to publish ${events.length} events`, error);
            throw error;
        }
    }
};
exports.EventStorePublisher = EventStorePublisher;
exports.EventStorePublisher = EventStorePublisher = EventStorePublisher_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [cqrs_1.EventBus])
], EventStorePublisher);
//# sourceMappingURL=event-publisher.js.map