import { Injectable, Logger } from '@nestjs/common';
import { WebSocketGateway } from './websocket.gateway';

export interface UnifiedRequestStatus {
  correlationId: string;
  userId: string;
  domain: string; // 'management-center', 'correspondence', etc.
  endpoint: string;
  method: string;
  status: 'received' | 'validated' | 'processing' | 'completed' | 'error';
  message: string;
  startTime: Date;
  lastUpdated: Date;
  data?: any;
  error?: string;
  progress?: number; // 0-100
}

export interface RequestResponse {
  correlationId: string;
  status: 'received_and_validated' | 'completed' | 'error';
  message: string;
  data?: any;
  error?: string;
  checkStatusUrl?: string;
}

@Injectable()
export class UnifiedRequestTrackerService {
  private readonly logger = new Logger(UnifiedRequestTrackerService.name);
  private requestStatuses = new Map<string, UnifiedRequestStatus>();
  
  // TTL for completed/error requests (in milliseconds)
  private readonly TTL_COMPLETED = 10 * 60 * 1000; // 10 minutes
  private readonly TTL_ACTIVE = 60 * 60 * 1000; // 60 minutes

  constructor(private readonly webSocketGateway: WebSocketGateway) {
    // Clean up old requests every 5 minutes
    setInterval(() => this.cleanupOldRequests(), 5 * 60 * 1000);
  }

  // Step 1: Client calls API with correlation ID in header
  // Server immediately responds with "received and validated"
  async handleInitialRequest(
    correlationId: string,
    userId: string,
    domain: string,
    endpoint: string,
    method: string,
  ): Promise<RequestResponse> {
    // Create request status
    const status: UnifiedRequestStatus = {
      correlationId,
      userId,
      domain,
      endpoint,
      method,
      status: 'received',
      message: `Request received: ${method} ${endpoint}`,
      startTime: new Date(),
      lastUpdated: new Date(),
      progress: 0,
    };

    this.requestStatuses.set(correlationId, status);
    this.logger.debug(`Created request tracking for ${correlationId} in domain ${domain}`);

    // Simulate validation (this would be actual validation in real implementation)
    await this.setValidated(correlationId);

    // Return immediate response
    return {
      correlationId,
      status: 'received_and_validated',
      message: 'Request received and validation passed. Processing started.',
      checkStatusUrl: `/api/${domain}/check-status/${correlationId}`,
    };
  }

  // Step 2: Set request as validated (called after auth/validation)
  async setValidated(correlationId: string): Promise<void> {
    const status = this.requestStatuses.get(correlationId);
    if (!status) return;

    status.status = 'validated';
    status.message = 'Request validated, authentication passed';
    status.progress = 25;
    status.lastUpdated = new Date();

    this.requestStatuses.set(correlationId, status);
    this.logger.debug(`Request ${correlationId} validated`);
  }

  // Step 3: Set request as processing
  async setProcessing(correlationId: string, operation?: string, progress?: number): Promise<void> {
    const status = this.requestStatuses.get(correlationId);
    if (!status) return;

    status.status = 'processing';
    status.message = operation ? `Processing: ${operation}` : 'Server is processing the request';
    status.progress = progress || 50;
    status.lastUpdated = new Date();

    this.requestStatuses.set(correlationId, status);
    this.logger.debug(`Request ${correlationId} processing: ${status.message}`);
  }

  // Step 4: Complete request - send via WebSocket if available, store for polling
  async completeRequest(correlationId: string, result: any): Promise<void> {
    const status = this.requestStatuses.get(correlationId);
    if (!status) return;

    status.status = 'completed';
    status.message = 'Request completed successfully';
    status.progress = 100;
    status.data = result;
    status.lastUpdated = new Date();

    this.requestStatuses.set(correlationId, status);

    // Try to send via WebSocket first
    const wsResponse: RequestResponse = {
      correlationId,
      status: 'completed',
      message: status.message,
      data: result,
    };

    const sent = this.webSocketGateway.sendToUser(status.userId, 'request-completed', wsResponse);
    
    if (sent) {
      this.logger.debug(`Request ${correlationId} completed - sent via WebSocket`);
    } else {
      this.logger.debug(`Request ${correlationId} completed - stored for polling`);
    }
  }

  // Step 4 (alternative): Fail request
  async failRequest(correlationId: string, error: string): Promise<void> {
    const status = this.requestStatuses.get(correlationId);
    if (!status) return;

    status.status = 'error';
    status.message = 'Request failed';
    status.error = error;
    status.lastUpdated = new Date();

    this.requestStatuses.set(correlationId, status);

    // Try to send via WebSocket first
    const wsResponse: RequestResponse = {
      correlationId,
      status: 'error',
      message: status.message,
      error,
    };

    const sent = this.webSocketGateway.sendToUser(status.userId, 'request-error', wsResponse);
    
    if (sent) {
      this.logger.debug(`Request ${correlationId} failed - sent via WebSocket`);
    } else {
      this.logger.debug(`Request ${correlationId} failed - stored for polling`);
    }
  }

  // Step 5: Client checks status (for polling clients)
  async getRequestStatus(correlationId: string, domain: string, userId: string): Promise<RequestResponse | null> {
    const status = this.requestStatuses.get(correlationId);
    
    if (!status) {
      return null;
    }

    // Verify domain and user access
    if (status.domain !== domain || status.userId !== userId) {
      return null;
    }

    // Convert internal status to response format
    const response: RequestResponse = {
      correlationId: status.correlationId,
      status: status.status === 'completed' ? 'completed' : 
              status.status === 'error' ? 'error' : 'received_and_validated',
      message: status.message,
      data: status.data,
      error: status.error,
    };

    this.logger.debug(`Status check for ${correlationId}: ${status.status}`);
    return response;
  }

  // Get all requests for a domain and user (admin/debugging)
  async getDomainRequests(domain: string, userId: string): Promise<UnifiedRequestStatus[]> {
    return Array.from(this.requestStatuses.values()).filter(
      status => status.domain === domain && status.userId === userId
    );
  }

  // Clean up old requests
  private cleanupOldRequests(): void {
    const now = new Date().getTime();
    let cleanedCount = 0;

    for (const [correlationId, status] of this.requestStatuses.entries()) {
      const age = now - status.lastUpdated.getTime();
      const isCompleted = ['completed', 'error'].includes(status.status);
      
      const shouldCleanup = isCompleted 
        ? age > this.TTL_COMPLETED 
        : age > this.TTL_ACTIVE;

      if (shouldCleanup) {
        this.requestStatuses.delete(correlationId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug(`Cleaned up ${cleanedCount} old request statuses`);
    }
  }

  // Get service statistics
  getStatistics() {
    const statuses = Array.from(this.requestStatuses.values());
    const byDomain = statuses.reduce((acc, status) => {
      acc[status.domain] = (acc[status.domain] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalRequests: statuses.length,
      activeRequests: statuses.filter(s => !['completed', 'error'].includes(s.status)).length,
      completedRequests: statuses.filter(s => s.status === 'completed').length,
      errorRequests: statuses.filter(s => s.status === 'error').length,
      byDomain,
      statusBreakdown: {
        received: statuses.filter(s => s.status === 'received').length,
        validated: statuses.filter(s => s.status === 'validated').length,
        processing: statuses.filter(s => s.status === 'processing').length,
        completed: statuses.filter(s => s.status === 'completed').length,
        error: statuses.filter(s => s.status === 'error').length,
      }
    };
  }
}
