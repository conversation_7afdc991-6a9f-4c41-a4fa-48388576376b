"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const swagger_1 = require("@nestjs/swagger");
const cqrs_1 = require("@nestjs/cqrs");
const users_service_1 = require("./users.service");
const create_user_dto_1 = require("./dto/create-user.dto");
const update_user_dto_1 = require("./dto/update-user.dto");
const common_2 = require("../../../../libs/common/src");
const create_user_command_1 = require("./commands/create-user.command");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const user_schema_1 = require("../auth/schemas/user.schema");
let UsersController = class UsersController {
    constructor(usersService, commandBus, queryBus) {
        this.usersService = usersService;
        this.commandBus = commandBus;
        this.queryBus = queryBus;
    }
    async create(createUserDto) {
        const command = new create_user_command_1.CreateUserCommand(createUserDto);
        const result = await this.commandBus.execute(command);
        return common_2.ApiResponseDto.success(result, 'User created successfully');
    }
    async findAll(pagination) {
        const result = await this.usersService.findAll(pagination);
        return common_2.ApiResponseDto.success(result, 'Users retrieved successfully');
    }
    async searchUsers(query, pagination) {
        const result = await this.usersService.searchUsers(query, pagination);
        return common_2.ApiResponseDto.success(result, 'Users search completed');
    }
    async findOne(id, currentUserId, currentUserRole) {
        if (id !== currentUserId && !this.hasElevatedPermissions(currentUserRole)) {
            const result = await this.usersService.findOne(currentUserId);
            return common_2.ApiResponseDto.success(result, 'User profile retrieved successfully');
        }
        const result = await this.usersService.findOne(id);
        return common_2.ApiResponseDto.success(result, 'User retrieved successfully');
    }
    async update(id, updateUserDto, currentUserId, currentUserRole) {
        if (id !== currentUserId && !this.hasElevatedPermissions(currentUserRole)) {
            const result = await this.usersService.update(currentUserId, updateUserDto);
            return common_2.ApiResponseDto.success(result, 'Profile updated successfully');
        }
        const result = await this.usersService.update(id, updateUserDto);
        return common_2.ApiResponseDto.success(result, 'User updated successfully');
    }
    async remove(id) {
        await this.usersService.remove(id);
        return common_2.ApiResponseDto.success(null, 'User deleted successfully');
    }
    hasElevatedPermissions(role) {
        return [user_schema_1.UserRole.MANAGER, user_schema_1.UserRole.ADMIN, user_schema_1.UserRole.SUPER_ADMIN].includes(role);
    }
    async handleGetUsers(data) {
        return this.usersService.findAll(data);
    }
    async handleGetUserById(data) {
        return this.usersService.findOne(data.id);
    }
    async handleCreateUser(data) {
        const command = new create_user_command_1.CreateUserCommand(data);
        return this.commandBus.execute(command);
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(user_schema_1.UserRole.ADMIN, user_schema_1.UserRole.SUPER_ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new user' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'User created successfully' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_dto_1.CreateUserDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(user_schema_1.UserRole.MANAGER, user_schema_1.UserRole.ADMIN, user_schema_1.UserRole.SUPER_ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get all users' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [common_2.PaginationDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, roles_decorator_1.Roles)(user_schema_1.UserRole.MANAGER, user_schema_1.UserRole.ADMIN, user_schema_1.UserRole.SUPER_ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Search users' }),
    __param(0, (0, common_1.Query)('q')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, common_2.PaginationDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "searchUsers", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(user_schema_1.UserRole.USER, user_schema_1.UserRole.MANAGER, user_schema_1.UserRole.ADMIN, user_schema_1.UserRole.SUPER_ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get user by ID' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, CurrentUser('id')),
    __param(2, CurrentUser('role')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)(user_schema_1.UserRole.USER, user_schema_1.UserRole.MANAGER, user_schema_1.UserRole.ADMIN, user_schema_1.UserRole.SUPER_ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Update user' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, CurrentUser('id')),
    __param(3, CurrentUser('role')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_dto_1.UpdateUserDto, String, String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(user_schema_1.UserRole.ADMIN, user_schema_1.UserRole.SUPER_ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete user' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "remove", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_users'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "handleGetUsers", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_user_by_id'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "handleGetUserById", null);
__decorate([
    (0, microservices_1.MessagePattern)('create_user'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_dto_1.CreateUserDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "handleCreateUser", null);
exports.UsersController = UsersController = __decorate([
    (0, swagger_1.ApiTags)('Users'),
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        cqrs_1.CommandBus,
        cqrs_1.QueryBus])
], UsersController);
//# sourceMappingURL=users.controller.js.map