import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { MongooseModule } from '@nestjs/mongoose';
import { CqrsModule } from '@nestjs/cqrs';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { User, UserSchema } from './schemas/user.schema';
import { RefreshToken, RefreshTokenSchema } from './schemas/refresh-token.schema';
import { RegisterUserHandler } from './commands/handlers/register-user.handler';
import { LoginUserHandler } from './commands/handlers/login-user.handler';
import { UserRegisteredHandler } from './events/handlers/user-registered.handler';
import { UserLoggedInHandler } from './events/handlers/user-logged-in.handler';
import { UserProfileUpdatedHandler } from './events/handlers/user-profile-updated.handler';
import { UserEmailVerifiedHandler } from './events/handlers/user-email-verified.handler';
import { UserPasswordResetHandler } from './events/handlers/user-password-reset.handler';

@Module({
  imports: [
    PassportModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your-secret-key',
      signOptions: { expiresIn: '24h' },
    }),
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema, connectionName: 'write' },
      { name: RefreshToken.name, schema: RefreshTokenSchema, connectionName: 'write' },
    ]),
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema, connectionName: 'read' },
      { name: RefreshToken.name, schema: RefreshTokenSchema, connectionName: 'read' },
    ]),
    CqrsModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtStrategy,
    LocalStrategy,
    JwtAuthGuard,
    LocalAuthGuard,
    RolesGuard,
    RegisterUserHandler,
    LoginUserHandler,
    UserRegisteredHandler,
    UserLoggedInHandler,
    UserProfileUpdatedHandler,
    UserEmailVerifiedHandler,
    UserPasswordResetHandler,
  ],
  exports: [AuthService, JwtAuthGuard, RolesGuard],
})
export class AuthModule {}
