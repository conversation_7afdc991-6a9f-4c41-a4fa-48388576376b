"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventHandler = exports.EVENT_HANDLER_METADATA = void 0;
const common_1 = require("@nestjs/common");
exports.EVENT_HANDLER_METADATA = 'EVENT_HANDLER_METADATA';
const EventHandler = (eventType, options) => (0, common_1.SetMetadata)(exports.EVENT_HANDLER_METADATA, {
    eventType,
    queue: options?.queue,
    routingKey: options?.routingKey,
});
exports.EventHandler = EventHandler;
//# sourceMappingURL=event-handler.decorator.js.map