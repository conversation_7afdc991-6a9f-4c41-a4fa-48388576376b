"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestStatusController = exports.RequestStatusListDto = exports.RequestStatusResponseDto = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const request_status_service_1 = require("./request-status.service");
const common_2 = require("../../../../libs/common/src");
class RequestStatusResponseDto {
}
exports.RequestStatusResponseDto = RequestStatusResponseDto;
class RequestStatusListDto {
}
exports.RequestStatusListDto = RequestStatusListDto;
let RequestStatusController = class RequestStatusController {
    constructor(requestStatusService) {
        this.requestStatusService = requestStatusService;
    }
    async getRequestStatus(requestId, req) {
        const user = req.user;
        const status = this.requestStatusService.getRequestStatus(requestId);
        if (!status) {
            return common_2.ApiResponseDto.error('Request not found', 404);
        }
        if (status.userId !== user.userId) {
            return common_2.ApiResponseDto.error('Access denied', 403);
        }
        const responseDto = {
            requestId: status.requestId,
            status: status.status,
            message: status.message,
            progress: status.progress,
            startTime: status.startTime,
            lastUpdated: status.lastUpdated,
            data: status.data,
            error: status.error,
        };
        return common_2.ApiResponseDto.success(responseDto, 'Request status retrieved');
    }
    async getUserRequestStatuses(activeOnly, req) {
        const user = req.user;
        const isActiveOnly = activeOnly === 'true';
        const statuses = isActiveOnly
            ? this.requestStatusService.getUserActiveRequests(user.userId)
            : this.requestStatusService.getUserRequestStatuses(user.userId);
        const responseDtos = statuses.map(status => ({
            requestId: status.requestId,
            status: status.status,
            message: status.message,
            progress: status.progress,
            startTime: status.startTime,
            lastUpdated: status.lastUpdated,
            data: status.data,
            error: status.error,
        }));
        const listDto = {
            requests: responseDtos,
            total: responseDtos.length,
            active: responseDtos.filter(r => !['completed', 'error'].includes(r.status)).length,
            completed: responseDtos.filter(r => r.status === 'completed').length,
            errors: responseDtos.filter(r => r.status === 'error').length,
        };
        return common_2.ApiResponseDto.success(listDto, 'Request statuses retrieved');
    }
    async pollRequestStatus(requestId, timeoutStr, req) {
        const user = req.user;
        const timeout = Math.min(parseInt(timeoutStr) || 30, 30) * 1000;
        const initialStatus = this.requestStatusService.getRequestStatus(requestId);
        if (!initialStatus) {
            return common_2.ApiResponseDto.error('Request not found', 404);
        }
        if (initialStatus.userId !== user.userId) {
            return common_2.ApiResponseDto.error('Access denied', 403);
        }
        if (['completed', 'error'].includes(initialStatus.status)) {
            const responseDto = {
                requestId: initialStatus.requestId,
                status: initialStatus.status,
                message: initialStatus.message,
                progress: initialStatus.progress,
                startTime: initialStatus.startTime,
                lastUpdated: initialStatus.lastUpdated,
                data: initialStatus.data,
                error: initialStatus.error,
            };
            return common_2.ApiResponseDto.success(responseDto, 'Request status retrieved');
        }
        const startTime = Date.now();
        const initialLastUpdated = initialStatus.lastUpdated.getTime();
        while (Date.now() - startTime < timeout) {
            const currentStatus = this.requestStatusService.getRequestStatus(requestId);
            if (!currentStatus) {
                return common_2.ApiResponseDto.error('Request not found', 404);
            }
            if (currentStatus.lastUpdated.getTime() > initialLastUpdated ||
                ['completed', 'error'].includes(currentStatus.status)) {
                const responseDto = {
                    requestId: currentStatus.requestId,
                    status: currentStatus.status,
                    message: currentStatus.message,
                    progress: currentStatus.progress,
                    startTime: currentStatus.startTime,
                    lastUpdated: currentStatus.lastUpdated,
                    data: currentStatus.data,
                    error: currentStatus.error,
                };
                return common_2.ApiResponseDto.success(responseDto, 'Request status updated');
            }
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        const finalStatus = this.requestStatusService.getRequestStatus(requestId);
        if (finalStatus) {
            const responseDto = {
                requestId: finalStatus.requestId,
                status: finalStatus.status,
                message: finalStatus.message,
                progress: finalStatus.progress,
                startTime: finalStatus.startTime,
                lastUpdated: finalStatus.lastUpdated,
                data: finalStatus.data,
                error: finalStatus.error,
            };
            return common_2.ApiResponseDto.success(responseDto, 'Request status (timeout reached)');
        }
        return common_2.ApiResponseDto.error('Request not found', 404);
    }
    async getStatistics() {
        const stats = this.requestStatusService.getStatistics();
        return common_2.ApiResponseDto.success(stats, 'Statistics retrieved');
    }
};
exports.RequestStatusController = RequestStatusController;
__decorate([
    (0, common_1.Get)(':requestId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get status of a specific request by correlation ID' }),
    (0, swagger_1.ApiParam)({ name: 'requestId', description: 'Request correlation ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Request status retrieved', type: RequestStatusResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Request not found' }),
    __param(0, (0, common_1.Param)('requestId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], RequestStatusController.prototype, "getRequestStatus", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all request statuses for the current user' }),
    (0, swagger_1.ApiQuery)({ name: 'active', required: false, description: 'Filter for active requests only' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Request statuses retrieved', type: RequestStatusListDto }),
    __param(0, (0, common_1.Query)('active')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], RequestStatusController.prototype, "getUserRequestStatuses", null);
__decorate([
    (0, common_1.Get)('poll/:requestId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Poll for request status with long polling support',
        description: 'Returns immediately if request is completed, or waits up to 30 seconds for status change'
    }),
    (0, swagger_1.ApiParam)({ name: 'requestId', description: 'Request correlation ID' }),
    (0, swagger_1.ApiQuery)({ name: 'timeout', required: false, description: 'Timeout in seconds (max 30)', example: 30 }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Request status (may be unchanged)', type: RequestStatusResponseDto }),
    __param(0, (0, common_1.Param)('requestId')),
    __param(1, (0, common_1.Query)('timeout')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], RequestStatusController.prototype, "pollRequestStatus", null);
__decorate([
    (0, common_1.Get)('admin/statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get request status statistics (admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Statistics retrieved' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RequestStatusController.prototype, "getStatistics", null);
exports.RequestStatusController = RequestStatusController = __decorate([
    (0, swagger_1.ApiTags)('Request Status'),
    (0, common_1.Controller)('request-status'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [request_status_service_1.RequestStatusService])
], RequestStatusController);
//# sourceMappingURL=request-status.controller.js.map