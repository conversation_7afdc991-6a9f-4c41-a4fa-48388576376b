"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_schema_1 = require("../auth/schemas/user.schema");
let UsersService = class UsersService {
    constructor(userWriteModel, userReadModel) {
        this.userWriteModel = userWriteModel;
        this.userReadModel = userReadModel;
    }
    async findAll(pagination) {
        const page = pagination?.page || 1;
        const limit = pagination?.limit || 10;
        const skip = (page - 1) * limit;
        const [users, total] = await Promise.all([
            this.userReadModel
                .find({ deletedAt: { $exists: false } })
                .skip(skip)
                .limit(limit)
                .sort({ createdAt: -1 })
                .exec(),
            this.userReadModel.countDocuments({ deletedAt: { $exists: false } }).exec(),
        ]);
        return { users, total };
    }
    async findOne(id) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.NotFoundException('Invalid user ID format');
        }
        const user = await this.userReadModel
            .findOne({ _id: id, deletedAt: { $exists: false } })
            .exec();
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
    async findByEmail(email) {
        return this.userReadModel
            .findOne({ email, deletedAt: { $exists: false } })
            .exec();
    }
    async findByOrganization(organizationId, pagination) {
        if (!mongoose_2.Types.ObjectId.isValid(organizationId)) {
            throw new common_1.NotFoundException('Invalid organization ID format');
        }
        const page = pagination?.page || 1;
        const limit = pagination?.limit || 10;
        const skip = (page - 1) * limit;
        const [users, total] = await Promise.all([
            this.userReadModel
                .find({
                organizationId: new mongoose_2.Types.ObjectId(organizationId),
                deletedAt: { $exists: false }
            })
                .skip(skip)
                .limit(limit)
                .sort({ createdAt: -1 })
                .exec(),
            this.userReadModel.countDocuments({
                organizationId: new mongoose_2.Types.ObjectId(organizationId),
                deletedAt: { $exists: false }
            }).exec(),
        ]);
        return { users, total };
    }
    async update(id, updateUserDto) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.NotFoundException('Invalid user ID format');
        }
        const user = await this.userWriteModel
            .findOneAndUpdate({ _id: id, deletedAt: { $exists: false } }, {
            ...updateUserDto,
            updatedAt: new Date(),
        }, { new: true })
            .exec();
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
    async updateStatus(id, status) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.NotFoundException('Invalid user ID format');
        }
        const user = await this.userWriteModel
            .findOneAndUpdate({ _id: id, deletedAt: { $exists: false } }, {
            status,
            updatedAt: new Date(),
        }, { new: true })
            .exec();
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
    async verifyEmail(id) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.NotFoundException('Invalid user ID format');
        }
        const user = await this.userWriteModel
            .findOneAndUpdate({ _id: id, deletedAt: { $exists: false } }, {
            emailVerified: true,
            status: user_schema_1.UserStatus.ACTIVE,
            emailVerificationToken: undefined,
            emailVerificationExpires: undefined,
            updatedAt: new Date(),
        }, { new: true })
            .exec();
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
    async remove(id) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.NotFoundException('Invalid user ID format');
        }
        const result = await this.userWriteModel
            .findOneAndUpdate({ _id: id, deletedAt: { $exists: false } }, {
            deletedAt: new Date(),
            updatedAt: new Date(),
        })
            .exec();
        if (!result) {
            throw new common_1.NotFoundException('User not found');
        }
    }
    async getActiveUsersCount() {
        return this.userReadModel.countDocuments({
            status: user_schema_1.UserStatus.ACTIVE,
            deletedAt: { $exists: false }
        }).exec();
    }
    async getUsersByRole(role) {
        return this.userReadModel
            .find({
            role,
            deletedAt: { $exists: false }
        })
            .sort({ createdAt: -1 })
            .exec();
    }
    async searchUsers(query, pagination) {
        const page = pagination?.page || 1;
        const limit = pagination?.limit || 10;
        const skip = (page - 1) * limit;
        const searchRegex = new RegExp(query, 'i');
        const searchFilter = {
            $or: [
                { firstName: searchRegex },
                { lastName: searchRegex },
                { email: searchRegex },
            ],
            deletedAt: { $exists: false }
        };
        const [users, total] = await Promise.all([
            this.userReadModel
                .find(searchFilter)
                .skip(skip)
                .limit(limit)
                .sort({ createdAt: -1 })
                .exec(),
            this.userReadModel.countDocuments(searchFilter).exec(),
        ]);
        return { users, total };
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name, 'write')),
    __param(1, (0, mongoose_1.InjectModel)(user_schema_1.User.name, 'read')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], UsersService);
//# sourceMappingURL=users.service.js.map