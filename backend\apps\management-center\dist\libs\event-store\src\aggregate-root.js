"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AggregateRoot = void 0;
const uuid_1 = require("uuid");
class AggregateRoot {
    constructor(id) {
        this.version = -1;
        this.uncommittedEvents = [];
        this.id = id || (0, uuid_1.v4)();
    }
    getId() {
        return this.id;
    }
    getVersion() {
        return this.version;
    }
    getUncommittedEvents() {
        return [...this.uncommittedEvents];
    }
    markEventsAsCommitted() {
        this.uncommittedEvents = [];
    }
    loadFromHistory(events) {
        events.forEach(event => {
            this.applyEvent(event, false);
            this.version = event.eventVersion;
        });
    }
    applyEvent(event, isNew = true) {
        const handler = this.getEventHandler(event.eventType);
        if (handler) {
            handler.call(this, event.eventData);
        }
        if (isNew) {
            this.version++;
            event.eventVersion = this.version;
            event.aggregateId = this.id;
            event.aggregateType = this.constructor.name;
            event.occurredAt = new Date();
            this.uncommittedEvents.push(event);
        }
    }
    raiseEvent(eventType, eventData, metadata) {
        const event = {
            eventId: (0, uuid_1.v4)(),
            eventType,
            aggregateId: this.id,
            aggregateType: this.constructor.name,
            eventVersion: this.version + 1,
            eventData,
            metadata,
            occurredAt: new Date()
        };
        this.applyEvent(event);
    }
    getEventHandler(eventType) {
        const handlerName = `on${eventType}`;
        return this[handlerName];
    }
}
exports.AggregateRoot = AggregateRoot;
//# sourceMappingURL=aggregate-root.js.map