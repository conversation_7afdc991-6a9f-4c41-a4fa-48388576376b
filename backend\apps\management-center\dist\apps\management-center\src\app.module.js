"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const mongoose_1 = require("@nestjs/mongoose");
const cqrs_1 = require("@nestjs/cqrs");
const event_store_1 = require("../../../libs/event-store/src");
const event_bus_1 = require("../../../libs/event-bus/src");
const users_module_1 = require("./users/users.module");
const organizations_module_1 = require("./organizations/organizations.module");
const settings_module_1 = require("./settings/settings.module");
const health_module_1 = require("./health/health.module");
const auth_module_1 = require("./auth/auth.module");
const websocket_module_1 = require("./websocket/websocket.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forRoot(process.env.MONGODB_WRITE_URI || 'mongodb://localhost:27017/management_center_write', {
                connectionName: 'write',
            }),
            mongoose_1.MongooseModule.forRoot(process.env.MONGODB_READ_URI || 'mongodb://localhost:27017/management_center_read', {
                connectionName: 'read',
            }),
            typeorm_1.TypeOrmModule.forRoot({
                type: 'postgres',
                host: process.env.DB_HOST || 'localhost',
                port: parseInt(process.env.DB_PORT || '5432'),
                username: process.env.DB_USERNAME || 'postgres',
                password: process.env.DB_PASSWORD || 'password',
                database: process.env.DB_NAME || 'management_center',
                entities: [__dirname + '/**/*.entity{.ts,.js}'],
                synchronize: process.env.NODE_ENV !== 'production',
                logging: process.env.NODE_ENV === 'development',
            }),
            cqrs_1.CqrsModule,
            event_store_1.EventStoreModule.forRoot({
                connectionString: process.env.EVENTSTORE_CONNECTION_STRING,
            }),
            event_bus_1.EventBusModule.forRoot({
                url: process.env.RABBITMQ_URL || 'amqp://admin:password@localhost:5672',
                exchange: process.env.RABBITMQ_EXCHANGE || 'enterprise.events',
                serviceName: 'management-center',
            }),
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            organizations_module_1.OrganizationsModule,
            settings_module_1.SettingsModule,
            health_module_1.HealthModule,
            websocket_module_1.WebSocketModule,
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map