"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRegisteredEvent = void 0;
class UserRegisteredEvent extends common_1.DomainEvent {
    constructor(userId, email, firstName, lastName, role, emailVerificationToken, organizationId) {
        super('User', userId, 'UserRegistered', 1);
        this.userId = userId;
        this.email = email;
        this.firstName = firstName;
        this.lastName = lastName;
        this.role = role;
        this.emailVerificationToken = emailVerificationToken;
        this.organizationId = organizationId;
    }
}
exports.UserRegisteredEvent = UserRegisteredEvent;
//# sourceMappingURL=user-registered.event.js.map