{"name": "@nestjs/websockets", "version": "10.4.19", "description": "Nest - modern, fast, powerful node.js web framework (@websockets)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/nestjs/nest.git", "directory": "packages/websockets"}, "publishConfig": {"access": "public"}, "dependencies": {"iterare": "1.2.1", "object-hash": "3.0.0", "tslib": "2.8.1"}, "devDependencies": {"@nestjs/common": "10.4.19", "@nestjs/core": "10.4.19"}, "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "reflect-metadata": "^0.1.12 || ^0.2.0", "rxjs": "^7.1.0"}, "peerDependenciesMeta": {"@nestjs/platform-socket.io": {"optional": true}}}