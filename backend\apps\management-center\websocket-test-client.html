<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket API Status Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .status.received { border-color: #2196F3; background: #E3F2FD; }
        .status.authenticated { border-color: #4CAF50; background: #E8F5E8; }
        .status.processing { border-color: #FF9800; background: #FFF3E0; }
        .status.completed { border-color: #4CAF50; background: #E8F5E8; }
        .status.error { border-color: #F44336; background: #FFEBEE; }
        .controls {
            margin: 20px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        input, button {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #2196F3;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #1976D2;
        }
        .log {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #fafafa;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 WebSocket API Status Test Client</h1>
        
        <div class="controls">
            <h3>Connection Settings</h3>
            <input type="text" id="serverUrl" value="ws://localhost:4001" placeholder="WebSocket Server URL">
            <input type="text" id="jwtToken" placeholder="JWT Token (Bearer token)" style="width: 300px;">
            <br>
            <button onclick="connectWebSocket()">Connect</button>
            <button onclick="disconnectWebSocket()">Disconnect</button>
            <span id="connectionStatus">Disconnected</span>
        </div>

        <div class="controls">
            <h3>API Testing</h3>
            <input type="text" id="apiUrl" value="http://localhost:4001/websocket-test/simulate-request" placeholder="API Endpoint">
            <select id="httpMethod">
                <option value="GET">GET</option>
                <option value="POST" selected>POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
            </select>
            <br>
            <textarea id="requestBody" placeholder="Request Body (JSON)" rows="3" style="width: 100%; margin: 5px 0;">{"operation": "create-user", "data": {"name": "Test User"}}</textarea>
            <br>
            <button onclick="makeApiRequest()">Send API Request</button>
            <label>
                <input type="checkbox" id="usePolling"> Use Polling (disable WebSocket)
            </label>
        </div>

        <div class="controls">
            <h3>Polling Test</h3>
            <input type="text" id="correlationId" placeholder="Correlation ID" style="width: 300px;">
            <button onclick="pollStatus()">Poll Status</button>
            <button onclick="startLongPolling()">Start Long Polling</button>
            <button onclick="stopLongPolling()">Stop Long Polling</button>
            <span id="pollingStatus">Not polling</span>
        </div>

        <div id="statusUpdates">
            <h3>📡 Real-time Status Updates</h3>
            <div id="statusContainer"></div>
        </div>

        <div>
            <h3>📋 WebSocket Log</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script>
        let socket = null;
        let requestId = null;
        let pollingInterval = null;
        let longPollingActive = false;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateConnectionStatus(status) {
            const statusSpan = document.getElementById('connectionStatus');
            statusSpan.textContent = status;
            statusSpan.style.color = status === 'Connected' ? 'green' : 'red';
        }

        function addStatusUpdate(status) {
            const container = document.getElementById('statusContainer');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${status.status}`;
            statusDiv.innerHTML = `
                <strong>${status.status.toUpperCase()}</strong> - ${status.message}
                <br><small>Request ID: ${status.requestId} | ${new Date(status.timestamp).toLocaleTimeString()}</small>
                ${status.data ? `<br><small>Data: ${JSON.stringify(status.data)}</small>` : ''}
                ${status.error ? `<br><small>Error: ${status.error}</small>` : ''}
            `;
            container.appendChild(statusDiv);
            container.scrollTop = container.scrollHeight;
        }

        function connectWebSocket() {
            const serverUrl = document.getElementById('serverUrl').value;
            const token = document.getElementById('jwtToken').value;

            if (!token) {
                alert('Please enter a JWT token');
                return;
            }

            try {
                socket = io(`${serverUrl}/api-status`, {
                    auth: {
                        token: token
                    }
                });

                socket.on('connect', () => {
                    log('✅ Connected to WebSocket server');
                    updateConnectionStatus('Connected');
                });

                socket.on('disconnect', () => {
                    log('❌ Disconnected from WebSocket server');
                    updateConnectionStatus('Disconnected');
                });

                socket.on('connected', (data) => {
                    log(`🎉 Connection confirmed: ${JSON.stringify(data)}`);
                });

                socket.on('request-status', (status) => {
                    log(`📡 Status update: ${JSON.stringify(status)}`);
                    addStatusUpdate(status);
                });

                socket.on('connect_error', (error) => {
                    log(`❌ Connection error: ${error.message}`);
                    updateConnectionStatus('Error');
                });

            } catch (error) {
                log(`❌ Failed to connect: ${error.message}`);
            }
        }

        function disconnectWebSocket() {
            if (socket) {
                socket.disconnect();
                socket = null;
                log('🔌 Disconnected from WebSocket');
                updateConnectionStatus('Disconnected');
            }
        }

        async function makeApiRequest() {
            const url = document.getElementById('apiUrl').value;
            const method = document.getElementById('httpMethod').value;
            const body = document.getElementById('requestBody').value;
            const token = document.getElementById('jwtToken').value;
            const usePolling = document.getElementById('usePolling').checked;

            if (!token) {
                alert('Please enter a JWT token');
                return;
            }

            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                };

                if (body && (method === 'POST' || method === 'PUT')) {
                    options.body = body;
                }

                log(`🚀 Making ${method} request to ${url}`);

                const response = await fetch(url, options);
                const result = await response.json();

                log(`📥 API Response (${response.status}): ${JSON.stringify(result)}`);

                // If response contains correlation ID and polling is enabled
                if (result.data && result.data.requestId && usePolling) {
                    const correlationId = result.data.requestId;
                    document.getElementById('correlationId').value = correlationId;
                    log(`🔗 Correlation ID: ${correlationId}`);
                    log(`📊 Status endpoint: ${result.data.statusEndpoint}`);
                    log(`🔄 Poll endpoint: ${result.data.pollEndpoint}`);

                    // Start polling automatically
                    startPollingForRequest(correlationId);
                }

            } catch (error) {
                log(`❌ API Request failed: ${error.message}`);
            }
        }

        async function pollStatus() {
            const correlationId = document.getElementById('correlationId').value;
            const token = document.getElementById('jwtToken').value;

            if (!correlationId || !token) {
                alert('Please enter correlation ID and JWT token');
                return;
            }

            try {
                const response = await fetch(`http://localhost:4001/request-status/${correlationId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const result = await response.json();
                log(`📊 Status Poll Result: ${JSON.stringify(result)}`);

                if (result.data) {
                    addStatusUpdate({
                        requestId: result.data.requestId,
                        status: result.data.status,
                        message: result.data.message,
                        timestamp: result.data.lastUpdated,
                        data: result.data.data,
                        error: result.data.error
                    });
                }

            } catch (error) {
                log(`❌ Status poll failed: ${error.message}`);
            }
        }

        async function startLongPolling() {
            const correlationId = document.getElementById('correlationId').value;
            if (!correlationId) {
                alert('Please enter correlation ID');
                return;
            }

            longPollingActive = true;
            document.getElementById('pollingStatus').textContent = 'Long polling active';
            document.getElementById('pollingStatus').style.color = 'green';

            await longPollRequest(correlationId);
        }

        function stopLongPolling() {
            longPollingActive = false;
            document.getElementById('pollingStatus').textContent = 'Long polling stopped';
            document.getElementById('pollingStatus').style.color = 'red';
        }

        async function longPollRequest(correlationId) {
            const token = document.getElementById('jwtToken').value;

            while (longPollingActive) {
                try {
                    log(`🔄 Long polling for ${correlationId}...`);

                    const response = await fetch(`http://localhost:4001/request-status/poll/${correlationId}?timeout=30`, {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    const result = await response.json();
                    log(`📊 Long Poll Result: ${JSON.stringify(result)}`);

                    if (result.data) {
                        addStatusUpdate({
                            requestId: result.data.requestId,
                            status: result.data.status,
                            message: result.data.message,
                            timestamp: result.data.lastUpdated,
                            data: result.data.data,
                            error: result.data.error
                        });

                        // Stop polling if completed or error
                        if (['completed', 'error'].includes(result.data.status)) {
                            longPollingActive = false;
                            document.getElementById('pollingStatus').textContent = 'Request completed';
                            document.getElementById('pollingStatus').style.color = 'blue';
                            break;
                        }
                    }

                } catch (error) {
                    log(`❌ Long poll failed: ${error.message}`);
                    await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds before retry
                }
            }
        }

        function startPollingForRequest(correlationId) {
            // Clear any existing polling
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            document.getElementById('correlationId').value = correlationId;

            // Poll every 2 seconds
            pollingInterval = setInterval(async () => {
                await pollStatus();
            }, 2000);

            log(`🔄 Started polling for request ${correlationId}`);
        }

        // Clear log function
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('statusContainer').innerHTML = '';
        }

        // Add clear button
        document.addEventListener('DOMContentLoaded', () => {
            const logDiv = document.querySelector('.log').parentNode;
            const clearButton = document.createElement('button');
            clearButton.textContent = 'Clear Log';
            clearButton.onclick = clearLog;
            clearButton.style.marginBottom = '10px';
            logDiv.insertBefore(clearButton, logDiv.lastElementChild);
        });
    </script>
</body>
</html>
