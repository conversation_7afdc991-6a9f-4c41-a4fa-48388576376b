<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unified API Request Flow Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .status.received { border-color: #2196F3; background: #E3F2FD; }
        .status.authenticated { border-color: #4CAF50; background: #E8F5E8; }
        .status.processing { border-color: #FF9800; background: #FFF3E0; }
        .status.completed { border-color: #4CAF50; background: #E8F5E8; }
        .status.error { border-color: #F44336; background: #FFEBEE; }
        .controls {
            margin: 20px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        input, button {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #2196F3;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #1976D2;
        }
        .log {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #fafafa;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Unified API Request Flow Test Client</h1>
        <p><strong>New Flow:</strong> Client generates correlation ID → Server responds immediately → Background processing → WebSocket push OR polling check</p>
        
        <div class="controls">
            <h3>Connection Settings</h3>
            <input type="text" id="serverUrl" value="ws://localhost:4001" placeholder="WebSocket Server URL">
            <input type="text" id="jwtToken" placeholder="JWT Token (Bearer token)" style="width: 300px;">
            <br>
            <button onclick="connectWebSocket()">Connect</button>
            <button onclick="disconnectWebSocket()">Disconnect</button>
            <span id="connectionStatus">Disconnected</span>
        </div>

        <div class="controls">
            <h3>Unified API Testing</h3>
            <input type="text" id="correlationIdInput" placeholder="Correlation ID (auto-generated if empty)" style="width: 300px;">
            <button onclick="generateCorrelationId()">Generate New ID</button>
            <br>
            <input type="text" id="apiUrl" value="http://localhost:4001/unified-test/simulate-request" placeholder="API Endpoint">
            <select id="httpMethod">
                <option value="GET">GET</option>
                <option value="POST" selected>POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
            </select>
            <br>
            <textarea id="requestBody" placeholder="Request Body (JSON)" rows="3" style="width: 100%; margin: 5px 0;">{"operation": "create-user", "data": {"name": "Test User"}}</textarea>
            <br>
            <button onclick="makeUnifiedApiRequest()">Send Unified Request</button>
            <label>
                <input type="checkbox" id="usePolling"> Disable WebSocket (polling only)
            </label>
        </div>

        <div class="controls">
            <h3>Status Checking</h3>
            <input type="text" id="statusCorrelationId" placeholder="Correlation ID for status check" style="width: 300px;">
            <select id="domainSelect">
                <option value="management-center" selected>management-center</option>
                <option value="correspondence">correspondence</option>
            </select>
            <br>
            <button onclick="checkStatus()">Check Status</button>
            <button onclick="startPolling()">Start Auto Polling</button>
            <button onclick="stopPolling()">Stop Polling</button>
            <span id="pollingStatus">Not polling</span>
        </div>

        <div id="statusUpdates">
            <h3>📡 Real-time Status Updates</h3>
            <div id="statusContainer"></div>
        </div>

        <div>
            <h3>📋 WebSocket Log</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script>
        let socket = null;
        let requestId = null;
        let pollingInterval = null;
        let longPollingActive = false;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateConnectionStatus(status) {
            const statusSpan = document.getElementById('connectionStatus');
            statusSpan.textContent = status;
            statusSpan.style.color = status === 'Connected' ? 'green' : 'red';
        }

        function addStatusUpdate(status) {
            const container = document.getElementById('statusContainer');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${status.status}`;
            statusDiv.innerHTML = `
                <strong>${status.status.toUpperCase()}</strong> - ${status.message}
                <br><small>Request ID: ${status.requestId} | ${new Date(status.timestamp).toLocaleTimeString()}</small>
                ${status.data ? `<br><small>Data: ${JSON.stringify(status.data)}</small>` : ''}
                ${status.error ? `<br><small>Error: ${status.error}</small>` : ''}
            `;
            container.appendChild(statusDiv);
            container.scrollTop = container.scrollHeight;
        }

        function connectWebSocket() {
            const serverUrl = document.getElementById('serverUrl').value;
            const token = document.getElementById('jwtToken').value;

            if (!token) {
                alert('Please enter a JWT token');
                return;
            }

            try {
                socket = io(`${serverUrl}/api-status`, {
                    auth: {
                        token: token
                    }
                });

                socket.on('connect', () => {
                    log('✅ Connected to WebSocket server');
                    updateConnectionStatus('Connected');
                });

                socket.on('disconnect', () => {
                    log('❌ Disconnected from WebSocket server');
                    updateConnectionStatus('Disconnected');
                });

                socket.on('connected', (data) => {
                    log(`🎉 Connection confirmed: ${JSON.stringify(data)}`);
                });

                socket.on('request-status', (status) => {
                    log(`📡 Status update: ${JSON.stringify(status)}`);
                    addStatusUpdate(status);
                });

                socket.on('request-completed', (response) => {
                    log(`✅ Request completed: ${JSON.stringify(response)}`);
                    addStatusUpdate({
                        requestId: response.correlationId,
                        status: 'completed',
                        message: response.message,
                        timestamp: new Date(),
                        data: response.data
                    });
                });

                socket.on('request-error', (response) => {
                    log(`❌ Request failed: ${JSON.stringify(response)}`);
                    addStatusUpdate({
                        requestId: response.correlationId,
                        status: 'error',
                        message: response.message,
                        timestamp: new Date(),
                        error: response.error
                    });
                });

                socket.on('connect_error', (error) => {
                    log(`❌ Connection error: ${error.message}`);
                    updateConnectionStatus('Error');
                });

            } catch (error) {
                log(`❌ Failed to connect: ${error.message}`);
            }
        }

        function disconnectWebSocket() {
            if (socket) {
                socket.disconnect();
                socket = null;
                log('🔌 Disconnected from WebSocket');
                updateConnectionStatus('Disconnected');
            }
        }

        async function makeApiRequest() {
            const url = document.getElementById('apiUrl').value;
            const method = document.getElementById('httpMethod').value;
            const body = document.getElementById('requestBody').value;
            const token = document.getElementById('jwtToken').value;
            const usePolling = document.getElementById('usePolling').checked;

            if (!token) {
                alert('Please enter a JWT token');
                return;
            }

            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                };

                if (body && (method === 'POST' || method === 'PUT')) {
                    options.body = body;
                }

                log(`🚀 Making ${method} request to ${url}`);

                const response = await fetch(url, options);
                const result = await response.json();

                log(`📥 API Response (${response.status}): ${JSON.stringify(result)}`);

                // If response contains correlation ID and polling is enabled
                if (result.data && result.data.requestId && usePolling) {
                    const correlationId = result.data.requestId;
                    document.getElementById('correlationId').value = correlationId;
                    log(`🔗 Correlation ID: ${correlationId}`);
                    log(`📊 Status endpoint: ${result.data.statusEndpoint}`);
                    log(`🔄 Poll endpoint: ${result.data.pollEndpoint}`);

                    // Start polling automatically
                    startPollingForRequest(correlationId);
                }

            } catch (error) {
                log(`❌ API Request failed: ${error.message}`);
            }
        }

        function generateCorrelationId() {
            const correlationId = 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            document.getElementById('correlationIdInput').value = correlationId;
            log(`🆔 Generated correlation ID: ${correlationId}`);
        }

        async function makeUnifiedApiRequest() {
            const url = document.getElementById('apiUrl').value;
            const method = document.getElementById('httpMethod').value;
            const body = document.getElementById('requestBody').value;
            const token = document.getElementById('jwtToken').value;
            const usePolling = document.getElementById('usePolling').checked;

            let correlationId = document.getElementById('correlationIdInput').value;
            if (!correlationId) {
                correlationId = 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                document.getElementById('correlationIdInput').value = correlationId;
            }

            if (!token) {
                alert('Please enter a JWT token');
                return;
            }

            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                        'X-Correlation-ID': correlationId
                    }
                };

                if (body && (method === 'POST' || method === 'PUT')) {
                    options.body = body;
                }

                log(`🚀 Making unified ${method} request to ${url}`);
                log(`🆔 Correlation ID: ${correlationId}`);

                const response = await fetch(url, options);
                const result = await response.json();

                log(`📥 Immediate Response (${response.status}): ${JSON.stringify(result)}`);

                // Set correlation ID for status checking
                document.getElementById('statusCorrelationId').value = correlationId;

                // If polling mode or no WebSocket, start checking status
                if (usePolling || !socket || socket.disconnected) {
                    log(`🔄 Starting status polling for ${correlationId}`);
                    setTimeout(() => startStatusPolling(correlationId), 2000);
                }

            } catch (error) {
                log(`❌ Unified API Request failed: ${error.message}`);
            }
        }

        async function checkStatus() {
            const correlationId = document.getElementById('statusCorrelationId').value;
            const domain = document.getElementById('domainSelect').value;
            const token = document.getElementById('jwtToken').value;

            if (!correlationId || !token) {
                alert('Please enter correlation ID and JWT token');
                return;
            }

            try {
                const response = await fetch(`http://localhost:4001/api/${domain}/check-status/${correlationId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const result = await response.json();
                log(`📊 Status Check Result: ${JSON.stringify(result)}`);

                if (result.data) {
                    addStatusUpdate({
                        requestId: result.data.correlationId,
                        status: result.data.status,
                        message: result.data.message,
                        timestamp: new Date(),
                        data: result.data.data,
                        error: result.data.error
                    });
                }

            } catch (error) {
                log(`❌ Status check failed: ${error.message}`);
            }
        }

        function startPolling() {
            const correlationId = document.getElementById('statusCorrelationId').value;
            if (!correlationId) {
                alert('Please enter correlation ID');
                return;
            }

            startStatusPolling(correlationId);
        }

        function stopPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
            }
            document.getElementById('pollingStatus').textContent = 'Polling stopped';
            document.getElementById('pollingStatus').style.color = 'red';
        }

        function startStatusPolling(correlationId) {
            // Clear any existing polling
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            document.getElementById('pollingStatus').textContent = 'Polling active';
            document.getElementById('pollingStatus').style.color = 'green';

            // Poll every 3 seconds
            pollingInterval = setInterval(async () => {
                const domain = document.getElementById('domainSelect').value;
                const token = document.getElementById('jwtToken').value;

                try {
                    const response = await fetch(`http://localhost:4001/api/${domain}/check-status/${correlationId}`, {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    const result = await response.json();

                    if (result.data) {
                        addStatusUpdate({
                            requestId: result.data.correlationId,
                            status: result.data.status,
                            message: result.data.message,
                            timestamp: new Date(),
                            data: result.data.data,
                            error: result.data.error
                        });

                        // Stop polling if completed or error
                        if (['completed', 'error'].includes(result.data.status)) {
                            clearInterval(pollingInterval);
                            pollingInterval = null;
                            document.getElementById('pollingStatus').textContent = 'Request completed';
                            document.getElementById('pollingStatus').style.color = 'blue';
                        }
                    }

                } catch (error) {
                    log(`❌ Polling failed: ${error.message}`);
                }
            }, 3000);

            log(`🔄 Started polling for request ${correlationId}`);
        }

        async function pollStatus() {
            const correlationId = document.getElementById('correlationId').value;
            const token = document.getElementById('jwtToken').value;

            if (!correlationId || !token) {
                alert('Please enter correlation ID and JWT token');
                return;
            }

            try {
                const response = await fetch(`http://localhost:4001/request-status/${correlationId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const result = await response.json();
                log(`📊 Status Poll Result: ${JSON.stringify(result)}`);

                if (result.data) {
                    addStatusUpdate({
                        requestId: result.data.requestId,
                        status: result.data.status,
                        message: result.data.message,
                        timestamp: result.data.lastUpdated,
                        data: result.data.data,
                        error: result.data.error
                    });
                }

            } catch (error) {
                log(`❌ Status poll failed: ${error.message}`);
            }
        }

        async function startLongPolling() {
            const correlationId = document.getElementById('correlationId').value;
            if (!correlationId) {
                alert('Please enter correlation ID');
                return;
            }

            longPollingActive = true;
            document.getElementById('pollingStatus').textContent = 'Long polling active';
            document.getElementById('pollingStatus').style.color = 'green';

            await longPollRequest(correlationId);
        }

        function stopLongPolling() {
            longPollingActive = false;
            document.getElementById('pollingStatus').textContent = 'Long polling stopped';
            document.getElementById('pollingStatus').style.color = 'red';
        }

        async function longPollRequest(correlationId) {
            const token = document.getElementById('jwtToken').value;

            while (longPollingActive) {
                try {
                    log(`🔄 Long polling for ${correlationId}...`);

                    const response = await fetch(`http://localhost:4001/request-status/poll/${correlationId}?timeout=30`, {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    const result = await response.json();
                    log(`📊 Long Poll Result: ${JSON.stringify(result)}`);

                    if (result.data) {
                        addStatusUpdate({
                            requestId: result.data.requestId,
                            status: result.data.status,
                            message: result.data.message,
                            timestamp: result.data.lastUpdated,
                            data: result.data.data,
                            error: result.data.error
                        });

                        // Stop polling if completed or error
                        if (['completed', 'error'].includes(result.data.status)) {
                            longPollingActive = false;
                            document.getElementById('pollingStatus').textContent = 'Request completed';
                            document.getElementById('pollingStatus').style.color = 'blue';
                            break;
                        }
                    }

                } catch (error) {
                    log(`❌ Long poll failed: ${error.message}`);
                    await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds before retry
                }
            }
        }

        function startPollingForRequest(correlationId) {
            // Clear any existing polling
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            document.getElementById('correlationId').value = correlationId;

            // Poll every 2 seconds
            pollingInterval = setInterval(async () => {
                await pollStatus();
            }, 2000);

            log(`🔄 Started polling for request ${correlationId}`);
        }

        // Clear log function
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('statusContainer').innerHTML = '';
        }

        // Add clear button
        document.addEventListener('DOMContentLoaded', () => {
            const logDiv = document.querySelector('.log').parentNode;
            const clearButton = document.createElement('button');
            clearButton.textContent = 'Clear Log';
            clearButton.onclick = clearLog;
            clearButton.style.marginBottom = '10px';
            logDiv.insertBefore(clearButton, logDiv.lastElementChild);
        });
    </script>
</body>
</html>
