"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketTestController = exports.TestRequestDto = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const unified_request_tracker_service_1 = require("./unified-request-tracker.service");
const correlation_id_interceptor_1 = require("./correlation-id.interceptor");
const common_2 = require("../../../../libs/common/src");
class TestRequestDto {
}
exports.TestRequestDto = TestRequestDto;
let WebSocketTestController = class WebSocketTestController {
    constructor(requestTracker) {
        this.requestTracker = requestTracker;
    }
    async simulateRequest(testRequest, correlationId, req) {
        const user = req.user;
        if (!correlationId) {
            return common_2.ApiResponseDto.error('X-Correlation-ID header is required', 400);
        }
        await new Promise(resolve => setTimeout(resolve, 3000));
        let result;
        switch (testRequest.operation) {
            case 'create-user':
                result = {
                    id: 'user-123',
                    email: '<EMAIL>',
                    created: true,
                    correlationId
                };
                break;
            case 'fetch-data':
                result = {
                    data: [1, 2, 3, 4, 5],
                    count: 5,
                    correlationId
                };
                break;
            case 'error-test':
                throw new Error('Simulated error for testing');
            default:
                result = {
                    message: 'Operation completed successfully',
                    operation: testRequest.operation,
                    data: testRequest.data,
                    correlationId
                };
        }
        return result;
    }
    async sendNotification(body, req) {
        const user = req.user;
        this.webSocketNotificationService.sendCustomNotification(user.userId, body.message, body.data);
        return common_2.ApiResponseDto.success({ sent: true }, 'Notification sent via WebSocket');
    }
    async broadcast(body) {
        this.webSocketNotificationService.broadcastNotification(body.message, body.data);
        return common_2.ApiResponseDto.success({ broadcasted: true }, 'Message broadcasted to all users');
    }
    async getStatus(req) {
        const user = req.user;
        return common_2.ApiResponseDto.success({
            activeRequests: this.webSocketNotificationService.getActiveRequestsCount(),
            userActiveRequests: this.webSocketNotificationService.getUserActiveRequests(user.userId).length,
            connectedClients: 'Available via WebSocket gateway',
        }, 'WebSocket service status retrieved');
    }
};
exports.WebSocketTestController = WebSocketTestController;
__decorate([
    (0, common_1.Post)('simulate-request'),
    (0, correlation_id_interceptor_1.UnifiedTracking)('Simulating unified request processing'),
    (0, swagger_1.ApiOperation)({
        summary: 'Test unified request flow with client-generated correlation ID',
        description: 'Client sends correlation ID in header. Server responds immediately with "received and validated", then processes in background. Final result sent via WebSocket or available via polling.'
    }),
    (0, swagger_1.ApiHeader)({ name: 'X-Correlation-ID', description: 'Client-generated correlation ID', required: true }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Request received and validated. Processing in background.' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)('x-correlation-id')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [TestRequestDto, String, Object]),
    __metadata("design:returntype", Promise)
], WebSocketTestController.prototype, "simulateRequest", null);
__decorate([
    (0, common_1.Post)('send-notification'),
    (0, swagger_1.ApiOperation)({ summary: 'Send a custom notification to the user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification sent' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebSocketTestController.prototype, "sendNotification", null);
__decorate([
    (0, common_1.Post)('broadcast'),
    (0, swagger_1.ApiOperation)({ summary: 'Broadcast a message to all connected users' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Message broadcasted' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebSocketTestController.prototype, "broadcast", null);
__decorate([
    (0, common_1.Get)('status'),
    (0, swagger_1.ApiOperation)({ summary: 'Get WebSocket service status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'WebSocket service status' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebSocketTestController.prototype, "getStatus", null);
exports.WebSocketTestController = WebSocketTestController = __decorate([
    (0, swagger_1.ApiTags)('Unified Request Test'),
    (0, common_1.Controller)('unified-test'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [unified_request_tracker_service_1.UnifiedRequestTrackerService])
], WebSocketTestController);
//# sourceMappingURL=websocket-test.controller.js.map