{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@enterprise/common": ["libs/common/src"], "@enterprise/event-store": ["libs/event-store/src"], "@enterprise/event-bus": ["libs/event-bus/src"]}}, "include": ["apps/**/*", "libs/**/*"], "exclude": ["node_modules", "dist"]}