"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const microservices_1 = require("@nestjs/microservices");
const common_1 = require("@nestjs/common");
const app_module_1 = require("./app.module");
const websocket_notification_interceptor_1 = require("./websocket/interceptors/websocket-notification.interceptor");
const websocket_notification_service_1 = require("./websocket/websocket-notification.service");
const correlation_id_interceptor_1 = require("./websocket/correlation-id.interceptor");
const unified_request_tracker_service_1 = require("./websocket/unified-request-tracker.service");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.connectMicroservice({
        transport: microservices_1.Transport.TCP,
        options: {
            host: '0.0.0.0',
            port: parseInt(process.env.MICROSERVICE_PORT) || 3001,
        },
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
    }));
    const reflector = app.get(core_1.Reflector);
    const notificationService = app.get(websocket_notification_service_1.WebSocketNotificationService);
    const unifiedTracker = app.get(unified_request_tracker_service_1.UnifiedRequestTrackerService);
    app.useGlobalInterceptors(new correlation_id_interceptor_1.CorrelationIdInterceptor(unifiedTracker, reflector), new websocket_notification_interceptor_1.WebSocketNotificationInterceptor(notificationService, reflector));
    await app.startAllMicroservices();
    const port = process.env.PORT || 4001;
    await app.listen(port);
    console.log(`🏢 Management Center Service is running on: http://localhost:${port}`);
    console.log(`🔌 Microservice listening on TCP port: ${process.env.MICROSERVICE_PORT || 3001}`);
}
bootstrap();
//# sourceMappingURL=main.js.map