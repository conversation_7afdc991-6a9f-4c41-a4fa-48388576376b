"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var EventStoreModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventStoreModule = void 0;
const common_1 = require("@nestjs/common");
const cqrs_1 = require("@nestjs/cqrs");
const event_store_service_1 = require("./event-store.service");
const event_publisher_1 = require("./event-publisher");
let EventStoreModule = EventStoreModule_1 = class EventStoreModule {
    static forRoot(options) {
        return {
            module: EventStoreModule_1,
            imports: [cqrs_1.CqrsModule],
            providers: [
                event_store_service_1.EventStoreService,
                event_publisher_1.EventStorePublisher,
                {
                    provide: 'EVENTSTORE_OPTIONS',
                    useValue: options || {}
                }
            ],
            exports: [event_store_service_1.EventStoreService, event_publisher_1.EventStorePublisher]
        };
    }
};
exports.EventStoreModule = EventStoreModule;
exports.EventStoreModule = EventStoreModule = EventStoreModule_1 = __decorate([
    (0, common_1.Module)({})
], EventStoreModule);
//# sourceMappingURL=event-store.module.js.map