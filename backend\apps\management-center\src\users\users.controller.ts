import { Controller, Get, Post, Body, Param, Put, Delete, Query, UseGuards } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ApiResponseDto, PaginationDto } from '@enterprise/common';
import { CreateUserCommand } from './commands/create-user.command';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../auth/schemas/user.schema';

@ApiTags('Users')
@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  async create(@Body() createUserDto: CreateUserDto) {
    const command = new CreateUserCommand(createUserDto);
    const result = await this.commandBus.execute(command);
    return ApiResponseDto.success(result, 'User created successfully');
  }

  @Get()
  @Roles(UserRole.MANAGER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Get all users' })
  async findAll(@Query() pagination: PaginationDto) {
    const result = await this.usersService.findAll(pagination);
    return ApiResponseDto.success(result, 'Users retrieved successfully');
  }

  @Get('search')
  @Roles(UserRole.MANAGER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Search users' })
  async searchUsers(@Query('q') query: string, @Query() pagination: PaginationDto) {
    const result = await this.usersService.searchUsers(query, pagination);
    return ApiResponseDto.success(result, 'Users search completed');
  }

  @Get(':id')
  @Roles(UserRole.USER, UserRole.MANAGER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Get user by ID' })
  async findOne(@Param('id') id: string, @CurrentUser('id') currentUserId: string, @CurrentUser('role') currentUserRole: UserRole) {
    // Users can only view their own profile unless they have elevated permissions
    if (id !== currentUserId && !this.hasElevatedPermissions(currentUserRole)) {
      const result = await this.usersService.findOne(currentUserId);
      return ApiResponseDto.success(result, 'User profile retrieved successfully');
    }

    const result = await this.usersService.findOne(id);
    return ApiResponseDto.success(result, 'User retrieved successfully');
  }

  @Put(':id')
  @Roles(UserRole.USER, UserRole.MANAGER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update user' })
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @CurrentUser('id') currentUserId: string,
    @CurrentUser('role') currentUserRole: UserRole
  ) {
    // Users can only update their own profile unless they have elevated permissions
    if (id !== currentUserId && !this.hasElevatedPermissions(currentUserRole)) {
      const result = await this.usersService.update(currentUserId, updateUserDto);
      return ApiResponseDto.success(result, 'Profile updated successfully');
    }

    const result = await this.usersService.update(id, updateUserDto);
    return ApiResponseDto.success(result, 'User updated successfully');
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete user' })
  async remove(@Param('id') id: string) {
    await this.usersService.remove(id);
    return ApiResponseDto.success(null, 'User deleted successfully');
  }

  private hasElevatedPermissions(role: UserRole): boolean {
    return [UserRole.MANAGER, UserRole.ADMIN, UserRole.SUPER_ADMIN].includes(role);
  }

  // Microservice message patterns
  @MessagePattern('get_users')
  async handleGetUsers(data: any) {
    return this.usersService.findAll(data);
  }

  @MessagePattern('get_user_by_id')
  async handleGetUserById(data: { id: string }) {
    return this.usersService.findOne(data.id);
  }

  @MessagePattern('create_user')
  async handleCreateUser(data: CreateUserDto) {
    const command = new CreateUserCommand(data);
    return this.commandBus.execute(command);
  }
}
