import { Controller, Get, Param, Query, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequestStatusService, RequestStatus } from './request-status.service';
import { ApiResponseDto } from '@enterprise/common';

export class RequestStatusResponseDto {
  requestId: string;
  status: 'received' | 'authenticated' | 'processing' | 'completed' | 'error';
  message: string;
  progress?: number;
  startTime: Date;
  lastUpdated: Date;
  data?: any;
  error?: string;
}

export class RequestStatusListDto {
  requests: RequestStatusResponseDto[];
  total: number;
  active: number;
  completed: number;
  errors: number;
}

@ApiTags('Request Status')
@Controller('request-status')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RequestStatusController {
  constructor(private readonly requestStatusService: RequestStatusService) {}

  @Get(':requestId')
  @ApiOperation({ summary: 'Get status of a specific request by correlation ID' })
  @ApiParam({ name: 'requestId', description: 'Request correlation ID' })
  @ApiResponse({ status: 200, description: 'Request status retrieved', type: RequestStatusResponseDto })
  @ApiResponse({ status: 404, description: 'Request not found' })
  async getRequestStatus(
    @Param('requestId') requestId: string,
    @Request() req: any,
  ) {
    const user = req.user;
    const status = this.requestStatusService.getRequestStatus(requestId);

    if (!status) {
      return ApiResponseDto.error('Request not found', 404);
    }

    // Ensure user can only access their own requests
    if (status.userId !== user.userId) {
      return ApiResponseDto.error('Access denied', 403);
    }

    const responseDto: RequestStatusResponseDto = {
      requestId: status.requestId,
      status: status.status,
      message: status.message,
      progress: status.progress,
      startTime: status.startTime,
      lastUpdated: status.lastUpdated,
      data: status.data,
      error: status.error,
    };

    return ApiResponseDto.success(responseDto, 'Request status retrieved');
  }

  @Get()
  @ApiOperation({ summary: 'Get all request statuses for the current user' })
  @ApiQuery({ name: 'active', required: false, description: 'Filter for active requests only' })
  @ApiResponse({ status: 200, description: 'Request statuses retrieved', type: RequestStatusListDto })
  async getUserRequestStatuses(
    @Query('active') activeOnly: string,
    @Request() req: any,
  ) {
    const user = req.user;
    const isActiveOnly = activeOnly === 'true';
    
    const statuses = isActiveOnly 
      ? this.requestStatusService.getUserActiveRequests(user.userId)
      : this.requestStatusService.getUserRequestStatuses(user.userId);

    const responseDtos: RequestStatusResponseDto[] = statuses.map(status => ({
      requestId: status.requestId,
      status: status.status,
      message: status.message,
      progress: status.progress,
      startTime: status.startTime,
      lastUpdated: status.lastUpdated,
      data: status.data,
      error: status.error,
    }));

    const listDto: RequestStatusListDto = {
      requests: responseDtos,
      total: responseDtos.length,
      active: responseDtos.filter(r => !['completed', 'error'].includes(r.status)).length,
      completed: responseDtos.filter(r => r.status === 'completed').length,
      errors: responseDtos.filter(r => r.status === 'error').length,
    };

    return ApiResponseDto.success(listDto, 'Request statuses retrieved');
  }

  @Get('poll/:requestId')
  @ApiOperation({ 
    summary: 'Poll for request status with long polling support',
    description: 'Returns immediately if request is completed, or waits up to 30 seconds for status change'
  })
  @ApiParam({ name: 'requestId', description: 'Request correlation ID' })
  @ApiQuery({ name: 'timeout', required: false, description: 'Timeout in seconds (max 30)', example: 30 })
  @ApiResponse({ status: 200, description: 'Request status (may be unchanged)', type: RequestStatusResponseDto })
  async pollRequestStatus(
    @Param('requestId') requestId: string,
    @Query('timeout') timeoutStr: string,
    @Request() req: any,
  ) {
    const user = req.user;
    const timeout = Math.min(parseInt(timeoutStr) || 30, 30) * 1000; // Max 30 seconds
    
    const initialStatus = this.requestStatusService.getRequestStatus(requestId);
    
    if (!initialStatus) {
      return ApiResponseDto.error('Request not found', 404);
    }

    // Ensure user can only access their own requests
    if (initialStatus.userId !== user.userId) {
      return ApiResponseDto.error('Access denied', 403);
    }

    // If already completed or error, return immediately
    if (['completed', 'error'].includes(initialStatus.status)) {
      const responseDto: RequestStatusResponseDto = {
        requestId: initialStatus.requestId,
        status: initialStatus.status,
        message: initialStatus.message,
        progress: initialStatus.progress,
        startTime: initialStatus.startTime,
        lastUpdated: initialStatus.lastUpdated,
        data: initialStatus.data,
        error: initialStatus.error,
      };
      return ApiResponseDto.success(responseDto, 'Request status retrieved');
    }

    // Long polling: wait for status change or timeout
    const startTime = Date.now();
    const initialLastUpdated = initialStatus.lastUpdated.getTime();

    while (Date.now() - startTime < timeout) {
      const currentStatus = this.requestStatusService.getRequestStatus(requestId);
      
      if (!currentStatus) {
        return ApiResponseDto.error('Request not found', 404);
      }

      // Check if status changed or completed
      if (currentStatus.lastUpdated.getTime() > initialLastUpdated || 
          ['completed', 'error'].includes(currentStatus.status)) {
        
        const responseDto: RequestStatusResponseDto = {
          requestId: currentStatus.requestId,
          status: currentStatus.status,
          message: currentStatus.message,
          progress: currentStatus.progress,
          startTime: currentStatus.startTime,
          lastUpdated: currentStatus.lastUpdated,
          data: currentStatus.data,
          error: currentStatus.error,
        };
        return ApiResponseDto.success(responseDto, 'Request status updated');
      }

      // Wait 1 second before checking again
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Timeout reached, return current status
    const finalStatus = this.requestStatusService.getRequestStatus(requestId);
    if (finalStatus) {
      const responseDto: RequestStatusResponseDto = {
        requestId: finalStatus.requestId,
        status: finalStatus.status,
        message: finalStatus.message,
        progress: finalStatus.progress,
        startTime: finalStatus.startTime,
        lastUpdated: finalStatus.lastUpdated,
        data: finalStatus.data,
        error: finalStatus.error,
      };
      return ApiResponseDto.success(responseDto, 'Request status (timeout reached)');
    }

    return ApiResponseDto.error('Request not found', 404);
  }

  @Get('admin/statistics')
  @ApiOperation({ summary: 'Get request status statistics (admin only)' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved' })
  async getStatistics() {
    const stats = this.requestStatusService.getStatistics();
    return ApiResponseDto.success(stats, 'Statistics retrieved');
  }
}
