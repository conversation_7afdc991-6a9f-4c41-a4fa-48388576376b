"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserLoggedInEvent = void 0;
class UserLoggedInEvent extends common_1.DomainEvent {
    constructor(userId, email, ipAddress, userAgent, loginTime) {
        super('User', userId, 'UserLoggedIn', 1);
        this.userId = userId;
        this.email = email;
        this.ipAddress = ipAddress;
        this.userAgent = userAgent;
        this.loginTime = loginTime;
    }
}
exports.UserLoggedInEvent = UserLoggedInEvent;
//# sourceMappingURL=user-logged-in.event.js.map