import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { WebSocketGateway } from './websocket.gateway';
import { WebSocketNotificationService } from './websocket-notification.service';
import { WsJwtGuard } from './guards/ws-jwt.guard';
import { WebSocketNotificationInterceptor } from './interceptors/websocket-notification.interceptor';
import { WebSocketTestController } from './websocket-test.controller';
import { RequestStatusService } from './request-status.service';
import { RequestStatusController } from './request-status.controller';
import { UnifiedRequestTrackerService } from './unified-request-tracker.service';
import { UnifiedStatusController, ManagementCenterStatusController } from './unified-status.controller';
import { CorrelationIdInterceptor } from './correlation-id.interceptor';

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your-secret-key',
      signOptions: { expiresIn: '24h' },
    }),
  ],
  controllers: [
    WebSocketTestController,
    RequestStatusController,
    UnifiedStatusController,
    ManagementCenterStatusController
  ],
  providers: [
    WebSocketGateway,
    WebSocketNotificationService,
    RequestStatusService,
    UnifiedRequestTrackerService,
    WsJwtGuard,
    WebSocketNotificationInterceptor,
    CorrelationIdInterceptor,
  ],
  exports: [
    WebSocketGateway,
    WebSocketNotificationService,
    RequestStatusService,
    UnifiedRequestTrackerService,
    WebSocketNotificationInterceptor,
    CorrelationIdInterceptor,
  ],
})
export class WebSocketModule {}
