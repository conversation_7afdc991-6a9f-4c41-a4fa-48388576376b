import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { WebSocketGateway } from './websocket.gateway';
import { WebSocketNotificationService } from './websocket-notification.service';
import { WsJwtGuard } from './guards/ws-jwt.guard';
import { WebSocketNotificationInterceptor } from './interceptors/websocket-notification.interceptor';
import { WebSocketTestController } from './websocket-test.controller';

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your-secret-key',
      signOptions: { expiresIn: '24h' },
    }),
  ],
  controllers: [WebSocketTestController],
  providers: [
    WebSocketGateway,
    WebSocketNotificationService,
    WsJwtGuard,
    WebSocketNotificationInterceptor,
  ],
  exports: [
    WebSocketGateway,
    WebSocketNotificationService,
    WebSocketNotificationInterceptor,
  ],
})
export class WebSocketModule {}
