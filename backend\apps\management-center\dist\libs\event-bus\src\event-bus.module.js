"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var EventBusModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventBusModule = void 0;
const common_1 = require("@nestjs/common");
const event_bus_service_1 = require("./event-bus.service");
const rabbitmq_event_publisher_1 = require("./rabbitmq-event-publisher");
const rabbitmq_event_subscriber_1 = require("./rabbitmq-event-subscriber");
let EventBusModule = EventBusModule_1 = class EventBusModule {
    static forRoot(config) {
        return {
            module: EventBusModule_1,
            providers: [
                {
                    provide: 'EVENT_BUS_CONFIG',
                    useValue: config,
                },
                {
                    provide: rabbitmq_event_publisher_1.RabbitMQEventPublisher,
                    useFactory: (config) => new rabbitmq_event_publisher_1.RabbitMQEventPublisher(config),
                    inject: ['EVENT_BUS_CONFIG'],
                },
                {
                    provide: rabbitmq_event_subscriber_1.RabbitMQEventSubscriber,
                    useFactory: (config) => new rabbitmq_event_subscriber_1.RabbitMQEventSubscriber(config),
                    inject: ['EVENT_BUS_CONFIG'],
                },
                event_bus_service_1.EventBusService,
            ],
            exports: [event_bus_service_1.EventBusService, rabbitmq_event_publisher_1.RabbitMQEventPublisher, rabbitmq_event_subscriber_1.RabbitMQEventSubscriber],
            global: true,
        };
    }
};
exports.EventBusModule = EventBusModule;
exports.EventBusModule = EventBusModule = EventBusModule_1 = __decorate([
    (0, common_1.Module)({})
], EventBusModule);
//# sourceMappingURL=event-bus.module.js.map