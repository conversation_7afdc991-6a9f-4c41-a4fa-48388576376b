"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RabbitMQEventPublisher_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RabbitMQEventPublisher = void 0;
const common_1 = require("@nestjs/common");
const amqp = require("amqplib");
let RabbitMQEventPublisher = RabbitMQEventPublisher_1 = class RabbitMQEventPublisher {
    constructor(config) {
        this.config = config;
        this.logger = new common_1.Logger(RabbitMQEventPublisher_1.name);
        this.isConnected = false;
    }
    async onModuleInit() {
        await this.connect();
    }
    async onModuleDestroy() {
        await this.disconnect();
    }
    async connect() {
        try {
            this.connection = await amqp.connect(this.config.url);
            this.channel = await this.connection.createChannel();
            await this.channel.assertExchange(this.config.exchange, 'topic', {
                durable: true,
            });
            this.isConnected = true;
            this.logger.log(`Connected to RabbitMQ at ${this.config.url}`);
            this.connection.on('error', (err) => {
                this.logger.error('RabbitMQ connection error:', err);
                this.isConnected = false;
            });
            this.connection.on('close', () => {
                this.logger.warn('RabbitMQ connection closed');
                this.isConnected = false;
            });
        }
        catch (error) {
            this.logger.error('Failed to connect to RabbitMQ:', error);
            throw error;
        }
    }
    async disconnect() {
        try {
            if (this.channel) {
                await this.channel.close();
            }
            if (this.connection) {
                await this.connection.close();
            }
            this.isConnected = false;
            this.logger.log('Disconnected from RabbitMQ');
        }
        catch (error) {
            this.logger.error('Error disconnecting from RabbitMQ:', error);
        }
    }
    async publish(event) {
        if (!this.isConnected) {
            await this.connect();
        }
        try {
            const routingKey = `${event.aggregateType}.${event.eventType}`;
            const message = Buffer.from(JSON.stringify(event));
            const published = this.channel.publish(this.config.exchange, routingKey, message, {
                persistent: true,
                messageId: event.eventId,
                timestamp: event.occurredAt.getTime(),
                headers: {
                    eventType: event.eventType,
                    aggregateType: event.aggregateType,
                    aggregateId: event.aggregateId,
                    eventVersion: event.eventVersion,
                    source: this.config.serviceName,
                },
            });
            if (!published) {
                throw new Error('Failed to publish event to RabbitMQ');
            }
            this.logger.log(`Published event ${event.eventType} for aggregate ${event.aggregateId} with routing key ${routingKey}`);
        }
        catch (error) {
            this.logger.error(`Failed to publish event ${event.eventType}:`, error);
            throw error;
        }
    }
    async publishBatch(events) {
        for (const event of events) {
            await this.publish(event);
        }
    }
};
exports.RabbitMQEventPublisher = RabbitMQEventPublisher;
exports.RabbitMQEventPublisher = RabbitMQEventPublisher = RabbitMQEventPublisher_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Object])
], RabbitMQEventPublisher);
//# sourceMappingURL=rabbitmq-event-publisher.js.map