"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RegisterUserHandler = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const bcrypt = require("bcrypt");
const uuid_1 = require("uuid");
const register_user_command_1 = require("../register-user.command");
const user_schema_1 = require("../../schemas/user.schema");
const user_registered_event_1 = require("../../events/user-registered.event");
let RegisterUserHandler = class RegisterUserHandler {
    constructor(userModel, eventBus) {
        this.userModel = userModel;
        this.eventBus = eventBus;
    }
    async execute(command) {
        const { registerDto } = command;
        const existingUser = await this.userModel.findOne({ email: registerDto.email }).exec();
        if (existingUser) {
            throw new common_1.ConflictException('User with this email already exists');
        }
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(registerDto.password, saltRounds);
        const emailVerificationToken = (0, uuid_1.v4)();
        const emailVerificationExpires = new Date();
        emailVerificationExpires.setHours(emailVerificationExpires.getHours() + 24);
        const user = new this.userModel({
            email: registerDto.email,
            password: hashedPassword,
            firstName: registerDto.firstName,
            lastName: registerDto.lastName,
            phone: registerDto.phone,
            role: registerDto.role || user_schema_1.UserRole.USER,
            organizationId: registerDto.organizationId ? new mongoose_2.Types.ObjectId(registerDto.organizationId) : undefined,
            emailVerificationToken,
            emailVerificationExpires,
        });
        const savedUser = await user.save();
        const event = new user_registered_event_1.UserRegisteredEvent(savedUser._id.toString(), savedUser.email, savedUser.firstName, savedUser.lastName, savedUser.role, emailVerificationToken, savedUser.organizationId?.toString());
        this.eventBus.publish(event);
        return savedUser;
    }
};
exports.RegisterUserHandler = RegisterUserHandler;
exports.RegisterUserHandler = RegisterUserHandler = __decorate([
    (0, cqrs_1.CommandHandler)(register_user_command_1.RegisterUserCommand),
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name, 'write')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        cqrs_1.EventBus])
], RegisterUserHandler);
//# sourceMappingURL=register-user.handler.js.map