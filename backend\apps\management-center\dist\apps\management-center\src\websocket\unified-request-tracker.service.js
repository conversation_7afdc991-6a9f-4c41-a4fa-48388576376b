"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UnifiedRequestTrackerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnifiedRequestTrackerService = void 0;
const common_1 = require("@nestjs/common");
const websocket_gateway_1 = require("./websocket.gateway");
let UnifiedRequestTrackerService = UnifiedRequestTrackerService_1 = class UnifiedRequestTrackerService {
    constructor(webSocketGateway) {
        this.webSocketGateway = webSocketGateway;
        this.logger = new common_1.Logger(UnifiedRequestTrackerService_1.name);
        this.requestStatuses = new Map();
        this.TTL_COMPLETED = 10 * 60 * 1000;
        this.TTL_ACTIVE = 60 * 60 * 1000;
        setInterval(() => this.cleanupOldRequests(), 5 * 60 * 1000);
    }
    async handleInitialRequest(correlationId, userId, domain, endpoint, method) {
        const status = {
            correlationId,
            userId,
            domain,
            endpoint,
            method,
            status: 'received',
            message: `Request received: ${method} ${endpoint}`,
            startTime: new Date(),
            lastUpdated: new Date(),
            progress: 0,
        };
        this.requestStatuses.set(correlationId, status);
        this.logger.debug(`Created request tracking for ${correlationId} in domain ${domain}`);
        await this.setValidated(correlationId);
        return {
            correlationId,
            status: 'received_and_validated',
            message: 'Request received and validation passed. Processing started.',
            checkStatusUrl: `/api/${domain}/check-status/${correlationId}`,
        };
    }
    async setValidated(correlationId) {
        const status = this.requestStatuses.get(correlationId);
        if (!status)
            return;
        status.status = 'validated';
        status.message = 'Request validated, authentication passed';
        status.progress = 25;
        status.lastUpdated = new Date();
        this.requestStatuses.set(correlationId, status);
        this.logger.debug(`Request ${correlationId} validated`);
    }
    async setProcessing(correlationId, operation, progress) {
        const status = this.requestStatuses.get(correlationId);
        if (!status)
            return;
        status.status = 'processing';
        status.message = operation ? `Processing: ${operation}` : 'Server is processing the request';
        status.progress = progress || 50;
        status.lastUpdated = new Date();
        this.requestStatuses.set(correlationId, status);
        this.logger.debug(`Request ${correlationId} processing: ${status.message}`);
    }
    async completeRequest(correlationId, result) {
        const status = this.requestStatuses.get(correlationId);
        if (!status)
            return;
        status.status = 'completed';
        status.message = 'Request completed successfully';
        status.progress = 100;
        status.data = result;
        status.lastUpdated = new Date();
        this.requestStatuses.set(correlationId, status);
        const wsResponse = {
            correlationId,
            status: 'completed',
            message: status.message,
            data: result,
        };
        const sent = this.webSocketGateway.sendToUser(status.userId, 'request-completed', wsResponse);
        if (sent) {
            this.logger.debug(`Request ${correlationId} completed - sent via WebSocket`);
        }
        else {
            this.logger.debug(`Request ${correlationId} completed - stored for polling`);
        }
    }
    async failRequest(correlationId, error) {
        const status = this.requestStatuses.get(correlationId);
        if (!status)
            return;
        status.status = 'error';
        status.message = 'Request failed';
        status.error = error;
        status.lastUpdated = new Date();
        this.requestStatuses.set(correlationId, status);
        const wsResponse = {
            correlationId,
            status: 'error',
            message: status.message,
            error,
        };
        const sent = this.webSocketGateway.sendToUser(status.userId, 'request-error', wsResponse);
        if (sent) {
            this.logger.debug(`Request ${correlationId} failed - sent via WebSocket`);
        }
        else {
            this.logger.debug(`Request ${correlationId} failed - stored for polling`);
        }
    }
    async getRequestStatus(correlationId, domain, userId) {
        const status = this.requestStatuses.get(correlationId);
        if (!status) {
            return null;
        }
        if (status.domain !== domain || status.userId !== userId) {
            return null;
        }
        const response = {
            correlationId: status.correlationId,
            status: status.status === 'completed' ? 'completed' :
                status.status === 'error' ? 'error' : 'received_and_validated',
            message: status.message,
            data: status.data,
            error: status.error,
        };
        this.logger.debug(`Status check for ${correlationId}: ${status.status}`);
        return response;
    }
    async getDomainRequests(domain, userId) {
        return Array.from(this.requestStatuses.values()).filter(status => status.domain === domain && status.userId === userId);
    }
    cleanupOldRequests() {
        const now = new Date().getTime();
        let cleanedCount = 0;
        for (const [correlationId, status] of this.requestStatuses.entries()) {
            const age = now - status.lastUpdated.getTime();
            const isCompleted = ['completed', 'error'].includes(status.status);
            const shouldCleanup = isCompleted
                ? age > this.TTL_COMPLETED
                : age > this.TTL_ACTIVE;
            if (shouldCleanup) {
                this.requestStatuses.delete(correlationId);
                cleanedCount++;
            }
        }
        if (cleanedCount > 0) {
            this.logger.debug(`Cleaned up ${cleanedCount} old request statuses`);
        }
    }
    getStatistics() {
        const statuses = Array.from(this.requestStatuses.values());
        const byDomain = statuses.reduce((acc, status) => {
            acc[status.domain] = (acc[status.domain] || 0) + 1;
            return acc;
        }, {});
        return {
            totalRequests: statuses.length,
            activeRequests: statuses.filter(s => !['completed', 'error'].includes(s.status)).length,
            completedRequests: statuses.filter(s => s.status === 'completed').length,
            errorRequests: statuses.filter(s => s.status === 'error').length,
            byDomain,
            statusBreakdown: {
                received: statuses.filter(s => s.status === 'received').length,
                validated: statuses.filter(s => s.status === 'validated').length,
                processing: statuses.filter(s => s.status === 'processing').length,
                completed: statuses.filter(s => s.status === 'completed').length,
                error: statuses.filter(s => s.status === 'error').length,
            }
        };
    }
};
exports.UnifiedRequestTrackerService = UnifiedRequestTrackerService;
exports.UnifiedRequestTrackerService = UnifiedRequestTrackerService = UnifiedRequestTrackerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [websocket_gateway_1.WebSocketGateway])
], UnifiedRequestTrackerService);
//# sourceMappingURL=unified-request-tracker.service.js.map