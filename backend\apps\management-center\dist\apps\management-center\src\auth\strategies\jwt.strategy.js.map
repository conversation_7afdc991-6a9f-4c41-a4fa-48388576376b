{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../../../../src/auth/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,+CAAoD;AACpD,+CAAoD;AACpD,2CAAmE;AACnE,+CAA+C;AAC/C,uCAAiC;AACjC,wDAA4D;AAYrD,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IACzD,YAEU,SAA8B;QAEtC,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,iBAAiB;SACzD,CAAC,CAAC;QANK,cAAS,GAAT,SAAS,CAAqB;IAOxC,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAmB;QAChC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAE/D,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,8BAAqB,CAAC,gBAAgB,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;YACrB,MAAM,IAAI,8BAAqB,CAAC,4BAA4B,CAAC,CAAC;QAChE,CAAC;QAED,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;YACvB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE;YAC/C,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,aAAa,EAAE,IAAI,CAAC,aAAa;SAClC,CAAC;IACJ,CAAC;CACF,CAAA;AAlCY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,sBAAW,EAAC,kBAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;qCACZ,gBAAK;GAHf,WAAW,CAkCvB"}