import { NestFactory, Reflector } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { WebSocketNotificationInterceptor } from './websocket/interceptors/websocket-notification.interceptor';
import { WebSocketNotificationService } from './websocket/websocket-notification.service';
import { CorrelationIdInterceptor } from './websocket/correlation-id.interceptor';
import { UnifiedRequestTrackerService } from './websocket/unified-request-tracker.service';

async function bootstrap() {
  // Create hybrid application (HTTP + TCP microservice)
  const app = await NestFactory.create(AppModule);

  // Configure as microservice
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.TCP,
    options: {
      host: '0.0.0.0',
      port: parseInt(process.env.MICROSERVICE_PORT) || 3001,
    },
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Global interceptors
  const reflector = app.get(Reflector);
  const notificationService = app.get(WebSocketNotificationService);
  const unifiedTracker = app.get(UnifiedRequestTrackerService);

  app.useGlobalInterceptors(
    new CorrelationIdInterceptor(unifiedTracker, reflector),
    new WebSocketNotificationInterceptor(notificationService, reflector),
  );

  // Start microservice
  await app.startAllMicroservices();

  // Start HTTP server for health checks and direct access
  const port = process.env.PORT || 4001;
  await app.listen(port);

  console.log(`🏢 Management Center Service is running on: http://localhost:${port}`);
  console.log(`🔌 Microservice listening on TCP port: ${process.env.MICROSERVICE_PORT || 3001}`);
}

bootstrap();
