import { Controller, Post, Body, UseGuards, Request, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { WebSocketNotificationService } from './websocket-notification.service';
import { ApiResponseDto } from '@enterprise/common';

export class TestRequestDto {
  operation: string;
  data?: any;
}

@ApiTags('WebSocket Test')
@Controller('websocket-test')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class WebSocketTestController {
  constructor(
    private readonly webSocketNotificationService: WebSocketNotificationService,
  ) {}

  @Post('simulate-request')
  @ApiOperation({
    summary: 'Simulate a request with real-time notifications',
    description: 'Returns correlation ID for polling if WebSocket is not available'
  })
  @ApiResponse({ status: 200, description: 'Request simulated with real-time updates' })
  async simulateRequest(@Body() testRequest: TestRequestDto, @Request() req: any) {
    const user = req.user;
    
    // Create request context and send "received" status
    const requestId = this.webSocketNotificationService.createRequestContext(
      user.userId,
      '/websocket-test/simulate-request',
      'POST'
    );

    try {
      // Send authentication passed status
      this.webSocketNotificationService.sendAuthenticationPassed(requestId, {
        id: user.userId,
        email: user.email,
        role: user.role,
      });

      // Send processing started status
      this.webSocketNotificationService.sendProcessingStarted(
        requestId, 
        testRequest.operation || 'Simulating request processing'
      );

      // Simulate some processing time
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate some work based on operation
      let result;
      switch (testRequest.operation) {
        case 'create-user':
          result = { 
            id: 'user-123', 
            email: '<EMAIL>', 
            created: true 
          };
          break;
        case 'fetch-data':
          result = { 
            data: [1, 2, 3, 4, 5], 
            count: 5 
          };
          break;
        case 'error-test':
          throw new Error('Simulated error for testing');
        default:
          result = { 
            message: 'Operation completed successfully',
            operation: testRequest.operation,
            data: testRequest.data 
          };
      }

      // Send completion status
      this.webSocketNotificationService.sendCompleted(requestId, result);

      return ApiResponseDto.success(
        {
          requestId,
          result,
          websocketSupported: true,
          statusEndpoint: `/request-status/${requestId}`,
          pollEndpoint: `/request-status/poll/${requestId}`,
        },
        'Request processed successfully with real-time notifications'
      );

    } catch (error) {
      // Send error status
      this.webSocketNotificationService.sendError(requestId, error);
      throw error;
    }
  }

  @Post('send-notification')
  @ApiOperation({ summary: 'Send a custom notification to the user' })
  @ApiResponse({ status: 200, description: 'Notification sent' })
  async sendNotification(@Body() body: { message: string; data?: any }, @Request() req: any) {
    const user = req.user;
    
    this.webSocketNotificationService.sendCustomNotification(
      user.userId,
      body.message,
      body.data
    );

    return ApiResponseDto.success(
      { sent: true }, 
      'Notification sent via WebSocket'
    );
  }

  @Post('broadcast')
  @ApiOperation({ summary: 'Broadcast a message to all connected users' })
  @ApiResponse({ status: 200, description: 'Message broadcasted' })
  async broadcast(@Body() body: { message: string; data?: any }) {
    this.webSocketNotificationService.broadcastNotification(
      body.message,
      body.data
    );

    return ApiResponseDto.success(
      { broadcasted: true }, 
      'Message broadcasted to all users'
    );
  }

  @Get('status')
  @ApiOperation({ summary: 'Get WebSocket service status' })
  @ApiResponse({ status: 200, description: 'WebSocket service status' })
  async getStatus(@Request() req: any) {
    const user = req.user;
    
    return ApiResponseDto.success({
      activeRequests: this.webSocketNotificationService.getActiveRequestsCount(),
      userActiveRequests: this.webSocketNotificationService.getUserActiveRequests(user.userId).length,
      connectedClients: 'Available via WebSocket gateway',
    }, 'WebSocket service status retrieved');
  }
}
