import { Controller, Post, Body, UseGuards, Request, Get, Headers } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiHeader } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UnifiedRequestTrackerService } from './unified-request-tracker.service';
import { UnifiedTracking } from './correlation-id.interceptor';
import { ApiResponseDto } from '@enterprise/common';

export class TestRequestDto {
  operation: string;
  data?: any;
}

@ApiTags('Unified Request Test')
@Controller('unified-test')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class WebSocketTestController {
  constructor(
    private readonly requestTracker: UnifiedRequestTrackerService,
  ) {}

  @Post('simulate-request')
  @UnifiedTracking('Simulating unified request processing')
  @ApiOperation({
    summary: 'Test unified request flow with client-generated correlation ID',
    description: 'Client sends correlation ID in header. Server responds immediately with "received and validated", then processes in background. Final result sent via WebSocket or available via polling.'
  })
  @ApiHeader({ name: 'X-Correlation-ID', description: 'Client-generated correlation ID', required: true })
  @ApiResponse({ status: 200, description: 'Request received and validated. Processing in background.' })
  async simulateRequest(
    @Body() testRequest: TestRequestDto,
    @Headers('x-correlation-id') correlationId: string,
    @Request() req: any
  ) {
    const user = req.user;

    if (!correlationId) {
      return ApiResponseDto.error('X-Correlation-ID header is required', 400);
    }

    // The interceptor handles the initial response
    // This method will process in background after immediate response

    // Simulate some processing time
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Simulate some work based on operation
    let result;
    switch (testRequest.operation) {
      case 'create-user':
        result = {
          id: 'user-123',
          email: '<EMAIL>',
          created: true,
          correlationId
        };
        break;
      case 'fetch-data':
        result = {
          data: [1, 2, 3, 4, 5],
          count: 5,
          correlationId
        };
        break;
      case 'error-test':
        throw new Error('Simulated error for testing');
      default:
        result = {
          message: 'Operation completed successfully',
          operation: testRequest.operation,
          data: testRequest.data,
          correlationId
        };
    }

    // Return the result (this will be sent via WebSocket or stored for polling)
    return result;
  }

  @Post('send-notification')
  @ApiOperation({ summary: 'Send a custom notification to the user' })
  @ApiResponse({ status: 200, description: 'Notification sent' })
  async sendNotification(@Body() body: { message: string; data?: any }, @Request() req: any) {
    const user = req.user;
    
    this.webSocketNotificationService.sendCustomNotification(
      user.userId,
      body.message,
      body.data
    );

    return ApiResponseDto.success(
      { sent: true }, 
      'Notification sent via WebSocket'
    );
  }

  @Post('broadcast')
  @ApiOperation({ summary: 'Broadcast a message to all connected users' })
  @ApiResponse({ status: 200, description: 'Message broadcasted' })
  async broadcast(@Body() body: { message: string; data?: any }) {
    this.webSocketNotificationService.broadcastNotification(
      body.message,
      body.data
    );

    return ApiResponseDto.success(
      { broadcasted: true }, 
      'Message broadcasted to all users'
    );
  }

  @Get('status')
  @ApiOperation({ summary: 'Get WebSocket service status' })
  @ApiResponse({ status: 200, description: 'WebSocket service status' })
  async getStatus(@Request() req: any) {
    const user = req.user;
    
    return ApiResponseDto.success({
      activeRequests: this.webSocketNotificationService.getActiveRequestsCount(),
      userActiveRequests: this.webSocketNotificationService.getUserActiveRequests(user.userId).length,
      connectedClients: 'Available via WebSocket gateway',
    }, 'WebSocket service status retrieved');
  }
}
