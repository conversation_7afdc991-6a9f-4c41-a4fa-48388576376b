import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallH<PERSON>ler,
  Logger,
} from '@nestjs/common';
import { Observable, tap, catchError, throwError } from 'rxjs';
import { UnifiedRequestTrackerService } from './unified-request-tracker.service';
import { Reflector } from '@nestjs/core';
import { v4 as uuidv4 } from 'uuid';

// Decorator to enable unified request tracking for specific endpoints
export const UNIFIED_TRACKING_KEY = 'unified_tracking';
export const UnifiedTracking = (operation?: string) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    Reflect.defineMetadata(UNIFIED_TRACKING_KEY, operation || true, descriptor.value);
  };
};

@Injectable()
export class CorrelationIdInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CorrelationIdInterceptor.name);

  constructor(
    private readonly requestTracker: UnifiedRequestTrackerService,
    private readonly reflector: Reflector,
  ) {}

  intercept(context: Execution<PERSON>ontext, next: <PERSON><PERSON><PERSON><PERSON>): Observable<any> {
    // Check if unified tracking is enabled for this endpoint
    const isEnabled = this.reflector.getAllAndOverride<boolean | string>(
      UNIFIED_TRACKING_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!isEnabled) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const user = request.user;

    // Skip if no authenticated user
    if (!user || !user.userId) {
      return next.handle();
    }

    // Get correlation ID from header (client-generated)
    let correlationId = request.headers['x-correlation-id'] || 
                       request.headers['correlation-id'];

    // If no correlation ID provided, generate one (fallback)
    if (!correlationId) {
      correlationId = uuidv4();
      this.logger.warn(`No correlation ID provided, generated: ${correlationId}`);
    }

    const endpoint = request.route?.path || request.url;
    const method = request.method;
    const domain = this.extractDomain(request);
    const operation = typeof isEnabled === 'string' ? isEnabled : undefined;

    this.logger.debug(`Processing request with correlation ID: ${correlationId}`);

    // Add correlation ID to request for later use
    request.correlationId = correlationId;

    // Handle initial request and get immediate response
    const handleInitialRequest = async () => {
      try {
        const initialResponse = await this.requestTracker.handleInitialRequest(
          correlationId,
          user.userId,
          domain,
          endpoint,
          method,
        );

        // Set correlation ID in response header
        response.setHeader('X-Correlation-ID', correlationId);

        return initialResponse;
      } catch (error) {
        this.logger.error(`Failed to handle initial request: ${error.message}`);
        throw error;
      }
    };

    // Process the initial request
    return new Observable(subscriber => {
      handleInitialRequest().then(initialResponse => {
        // Set processing status
        this.requestTracker.setProcessing(correlationId, operation);

        // Continue with the actual request processing
        next.handle().pipe(
          tap(async (result) => {
            // Complete the request with the result
            await this.requestTracker.completeRequest(correlationId, {
              success: true,
              data: result,
              endpoint,
              method,
            });
          }),
          catchError(async (error) => {
            // Fail the request with error details
            await this.requestTracker.failRequest(
              correlationId, 
              error.message || 'Request processing failed'
            );
            return throwError(() => error);
          }),
        ).subscribe({
          next: (result) => {
            // Return the initial response immediately, not the final result
            // The final result will be sent via WebSocket or available via polling
            subscriber.next({
              ...initialResponse,
              message: 'Request received and validated. Processing in background.',
              processingInBackground: true,
            });
            subscriber.complete();
          },
          error: (error) => {
            subscriber.error(error);
          }
        });
      }).catch(error => {
        subscriber.error(error);
      });
    });
  }

  private extractDomain(request: any): string {
    // Extract domain from request path or headers
    const path = request.url || '';
    
    // Check if it's a management-center request
    if (path.includes('/users') || path.includes('/organizations') || path.includes('/settings')) {
      return 'management-center';
    }
    
    // Check if it's a correspondence request
    if (path.includes('/correspondence') || path.includes('/messages')) {
      return 'correspondence';
    }
    
    // Check for domain in headers
    const domain = request.headers['x-service-domain'];
    if (domain) {
      return domain;
    }
    
    // Default to management-center
    return 'management-center';
  }
}
