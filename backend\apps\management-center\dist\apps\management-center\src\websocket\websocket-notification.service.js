"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WebSocketNotificationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketNotificationService = void 0;
const common_1 = require("@nestjs/common");
const websocket_gateway_1 = require("./websocket.gateway");
const uuid_1 = require("uuid");
let WebSocketNotificationService = WebSocketNotificationService_1 = class WebSocketNotificationService {
    constructor(webSocketGateway) {
        this.webSocketGateway = webSocketGateway;
        this.logger = new common_1.Logger(WebSocketNotificationService_1.name);
        this.activeRequests = new Map();
    }
    createRequestContext(userId, endpoint, method) {
        const requestId = (0, uuid_1.v4)();
        const context = {
            requestId,
            userId,
            endpoint,
            method,
            startTime: new Date(),
        };
        this.activeRequests.set(requestId, context);
        this.sendStatus(requestId, 'received', `Request received: ${method} ${endpoint}`);
        this.logger.log(`Created request context ${requestId} for user ${userId}`);
        return requestId;
    }
    sendAuthenticationPassed(requestId, userInfo) {
        const message = userInfo
            ? `Authentication passed for user: ${userInfo.email || userInfo.id}`
            : 'Authentication passed';
        this.sendStatus(requestId, 'authenticated', message, userInfo);
    }
    sendProcessingStarted(requestId, operation) {
        const message = operation
            ? `Processing started: ${operation}`
            : 'Server is processing the request';
        this.sendStatus(requestId, 'processing', message);
    }
    sendCompleted(requestId, result) {
        this.sendStatus(requestId, 'completed', 'Request completed successfully', result);
        this.cleanupRequest(requestId);
    }
    sendError(requestId, error) {
        const errorMessage = error instanceof Error ? error.message : error;
        this.sendStatus(requestId, 'error', 'Request failed', undefined, errorMessage);
        this.cleanupRequest(requestId);
    }
    sendStatus(requestId, status, message, data, error) {
        const context = this.activeRequests.get(requestId);
        if (!context) {
            this.logger.warn(`Request context not found for ID: ${requestId}`);
            return;
        }
        const statusMessage = {
            requestId,
            status,
            message,
            timestamp: new Date(),
            data,
            error,
        };
        this.webSocketGateway.sendStatusToUser(context.userId, statusMessage);
        this.webSocketGateway.sendStatusToRequest(requestId, statusMessage);
        this.logger.debug(`Sent ${status} status for request ${requestId}`);
    }
    cleanupRequest(requestId) {
        this.activeRequests.delete(requestId);
        this.logger.debug(`Cleaned up request context ${requestId}`);
    }
    getActiveRequestsCount() {
        return this.activeRequests.size;
    }
    getUserActiveRequests(userId) {
        return Array.from(this.activeRequests.values()).filter(context => context.userId === userId);
    }
    getAllActiveRequests() {
        return Array.from(this.activeRequests.values());
    }
    sendCustomNotification(userId, message, data) {
        const customMessage = {
            requestId: 'notification',
            status: 'completed',
            message,
            timestamp: new Date(),
            data,
        };
        this.webSocketGateway.sendStatusToUser(userId, customMessage);
        this.logger.debug(`Sent custom notification to user ${userId}: ${message}`);
    }
    broadcastNotification(message, data) {
        const broadcastMessage = {
            requestId: 'broadcast',
            status: 'completed',
            message,
            timestamp: new Date(),
            data,
        };
        this.webSocketGateway.broadcastStatus(broadcastMessage);
        this.logger.debug(`Broadcasted notification: ${message}`);
    }
};
exports.WebSocketNotificationService = WebSocketNotificationService;
exports.WebSocketNotificationService = WebSocketNotificationService = WebSocketNotificationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [websocket_gateway_1.WebSocketGateway])
], WebSocketNotificationService);
//# sourceMappingURL=websocket-notification.service.js.map