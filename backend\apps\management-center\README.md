# Management Center Service

A comprehensive user management and authentication service built with NestJS, MongoDB, EventStore, and RabbitMQ.

## Features

### Authentication & Authorization
- **User Registration**: Complete registration flow with email verification
- **JWT Authentication**: Secure token-based authentication with refresh tokens
- **Role-Based Access Control**: Support for USER, MANAGER, ADMIN, and SUPER_ADMIN roles
- **Password Management**: Secure password hashing, reset, and validation
- **Email Verification**: Email verification system with token expiration
- **Multi-Factor Security**: IP tracking, user agent logging, and session management

### Database Architecture
- **MongoDB Integration**: Separate read/write database connections for scalability
- **Event Sourcing**: Complete event sourcing with EventStore integration
- **CQRS Pattern**: Command Query Responsibility Segregation implementation
- **Data Validation**: Comprehensive schema validation and business rules

### Event-Driven Architecture
- **Domain Events**: User registration, login, profile updates, email verification
- **RabbitMQ Integration**: Distributed event publishing for microservices communication
- **Event Store**: Persistent event storage for audit trails and replay capabilities
- **Event Handlers**: Asynchronous event processing with error handling

## API Endpoints

### Authentication
- `POST /auth/register` - Register a new user
- `POST /auth/login` - User login
- `POST /auth/refresh` - Refresh access token
- `POST /auth/logout` - User logout
- `GET /auth/profile` - Get current user profile
- `POST /auth/verify-email` - Verify user email
- `POST /auth/resend-verification` - Resend verification email
- `POST /auth/forgot-password` - Request password reset
- `POST /auth/reset-password` - Reset password
- `POST /auth/revoke-all-tokens` - Revoke all refresh tokens

### User Management
- `GET /users` - Get all users (Admin/Manager only)
- `GET /users/search` - Search users (Admin/Manager only)
- `GET /users/:id` - Get user by ID
- `PUT /users/:id` - Update user profile
- `DELETE /users/:id` - Delete user (Admin only)
- `GET /users/organization/:id` - Get users by organization

## Environment Variables

```env
# MongoDB Configuration
MONGODB_WRITE_URI=*********************************************************************************
MONGODB_READ_URI=********************************************************************************

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# EventStore Configuration
EVENTSTORE_CONNECTION_STRING=esdb://localhost:2113?tls=false

# RabbitMQ Configuration
RABBITMQ_URL=amqp://admin:password@localhost:5672
RABBITMQ_EXCHANGE=enterprise.events
```

## Database Schema

### Users Collection
```javascript
{
  _id: ObjectId,
  email: String (unique, required),
  password: String (hashed, required),
  firstName: String (required),
  lastName: String (required),
  phone: String (optional),
  role: String (enum: user, manager, admin, super_admin),
  status: String (enum: active, inactive, suspended, pending),
  emailVerified: Boolean (default: false),
  emailVerificationToken: String (optional),
  emailVerificationExpires: Date (optional),
  passwordResetToken: String (optional),
  passwordResetExpires: Date (optional),
  organizationId: ObjectId (optional),
  lastLoginAt: Date (optional),
  createdAt: Date,
  updatedAt: Date,
  deletedAt: Date (optional)
}
```

### Refresh Tokens Collection
```javascript
{
  _id: ObjectId,
  token: String (unique, required),
  userId: ObjectId (required, ref: User),
  expiresAt: Date (required),
  revoked: Boolean (default: false),
  revokedAt: Date (optional),
  replacedByToken: String (optional),
  userAgent: String (optional),
  ipAddress: String (optional),
  createdAt: Date,
  updatedAt: Date
}
```

## Domain Events

### User Registration
- `UserRegisteredEvent`: Published when a new user registers
- Contains: userId, email, firstName, lastName, role, emailVerificationToken

### User Authentication
- `UserLoggedInEvent`: Published when a user successfully logs in
- Contains: userId, email, ipAddress, userAgent, loginTime

### User Profile Management
- `UserProfileUpdatedEvent`: Published when user profile is updated
- `UserEmailVerifiedEvent`: Published when user verifies their email
- `UserPasswordResetEvent`: Published when user resets their password

## Security Features

### Password Security
- Minimum 8 characters with complexity requirements
- BCrypt hashing with salt rounds of 12
- Password reset tokens with 1-hour expiration
- Automatic token revocation on password reset

### Token Management
- JWT access tokens with 24-hour expiration
- Refresh tokens with 7-day expiration
- Automatic token rotation on refresh
- IP address and user agent tracking
- Bulk token revocation for security incidents

### Role-Based Access Control
- Hierarchical role system (USER < MANAGER < ADMIN < SUPER_ADMIN)
- Route-level authorization guards
- Resource-level access control
- Self-service profile management for users

## Development Setup

1. Install dependencies:
```bash
npm install
```

2. Start MongoDB and other services:
```bash
docker-compose up -d mongodb-management-write mongodb-management-read eventstore rabbitmq
```

3. Run the service:
```bash
npm run start:dev
```

4. Access the API documentation:
- Swagger UI: http://localhost:4001/api
- Health Check: http://localhost:4001/health

## Testing

Run the test suite:
```bash
npm run test
npm run test:e2e
npm run test:cov
```

## Production Considerations

### Security
- Change default JWT secret in production
- Use strong MongoDB credentials
- Enable SSL/TLS for all connections
- Implement rate limiting
- Add request logging and monitoring

### Scalability
- Configure MongoDB replica sets for high availability
- Use Redis for session storage in multi-instance deployments
- Implement horizontal scaling with load balancers
- Monitor event processing performance

### Monitoring
- Set up application performance monitoring
- Configure log aggregation
- Monitor database performance
- Track event processing metrics
- Set up health check endpoints
